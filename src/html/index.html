<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<title>Стартовый шаблон | ВебКадеми</title>
	<link rel="stylesheet" href="./css/main.css" />

	<link rel="icon" type="image/x-icon" href="./img/favicons/favicon.svg">
	<link rel="apple-touch-icon" sizes="180x180" href="./img/favicons/apple-touch-icon.png">
</head>

<body>
	@@include('blocks/header.html')
	<main class="docs">
		<section>
			<div class="container">
				<h1 class="title-1">Gulp сборка для верстки сайтов</h1>
				<p>Понятная Gulp сборка которая ускорит твою верстку в разы.</p>
				<p>Документация по возможностям с примерами использования <a href="./docs.html">здесь</a>.</p>

				<!-- Теги img теперь можно писать в несколько строк, ошибки не будет -->
				<img
					src="./../img/project-02.jpg" alt="">

			</div>
		</section>
	</main>
	@@include('blocks/footer.html')
	<script src="./js/index.bundle.js"></script>
</body>

</html>