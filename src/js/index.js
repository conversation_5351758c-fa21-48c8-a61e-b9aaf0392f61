// jQuery
import $ from 'jquery';
window.jQuery = $;
window.$ = $;

// Галерея и лайтбоксы от Fancybox
import { Fancybox } from '@fancyapps/ui';
import '@fancyapps/ui/dist/fancybox/fancybox.css';

Fancybox.bind('[data-fancybox]', {
	// Your custom options
});




//#region DOM Loaded - меню deskt и mobile
document.addEventListener('DOMContentLoaded', function () {
  var pcMenu = document.querySelector('.main-menu');
  var mainMenuBtn = pcMenu.querySelector('.main-menu-btn-desktop');
  var body = document.querySelector('body');

  mainMenuBtn.addEventListener('click', () => {
    pcMenu.classList.toggle('active');
    if (pcMenu.classList.contains('active')) {
      mainMenuBtn.setAttribute('data-cursor-bubble-text', 'Свернуть меню');
    } else {
      mainMenuBtn.setAttribute('data-cursor-bubble-text', 'Раскрыть меню');
    }
  });

  // обработчик — по клику вне области закрываем desktop-меню
  document.addEventListener('click', function (event) {
    const Deskmenu = document.querySelector('.main-menu');
    if (!Deskmenu.contains(event.target) && !mainMenuBtn.contains(event.target)) {
      pcMenu.classList.remove('active');
    }
  });

  // кнопка меню мобильный добавляет класс в боди и появляется меню
  const mainMenuBtnMobile = document.querySelector('.main-menu-btn')
  mainMenuBtnMobile.addEventListener('click', () => {
    body.classList.toggle('main-menu-open')
  })
  // конец кнопка меню мобильный добавляет класс в боди и появляется меню

});

$(document).ready(function () {
  $('ul.main-menu__catalog-uslug').on('click', 'li:not(.catalog-uslug__item--link)', function () {
    if ($(this).hasClass('active')) {
      $(this).removeClass('active');
      $(this).children('.catalog-menu__list').slideUp();
    } else {
      $('ul.main-menu__catalog-uslug li.active').removeClass('active');
      $('ul.main-menu__catalog-uslug li .catalog-menu__list').slideUp();
      $(this).addClass('active');
      $(this).children('.catalog-menu__list').slideDown();
    }
  });
});
//#endregion