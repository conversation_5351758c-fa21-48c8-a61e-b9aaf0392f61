.none {
	display: none !important;
}

.visually-hidden {
	position: absolute;
	width: 1px;
	height: 1px;
	margin: -1px;
	border: 0;
	padding: 0;
	white-space: nowrap;
	clip-path: inset(100%);
	clip: rect(0 0 0 0);
	overflow: hidden;
}

.no-scroll {
	overflow-y: hidden;
}

@media only screen and (max-width:576px) {
    .ssm-hidden {
        display: none !important
    }
}
@media only screen and (max-width:767px) {
    .sm-hidden {
        display: none !important
    }
    .sm-center {
        text-align: center;
        margin: 0 auto;
    }
    .mt-2-only-sm {
        margin-top:2rem;
    }
}

@media only screen and (max-width:992px) {
    .md-hidden {
        display: none !important
    }
}

@media only screen and (max-width:1200px) {
    .lg-hidden {
        display: none !important
    }
}
@media only screen and (min-width:768px) {
    .sm-visible {
        display: none !important
    }
}

@media only screen and (min-width:993px) {
    .md-visible {
        display: none !important
    }
}

@media only screen and (min-width:1201px) {
    .lg-visible {
        display: none !important
    }
}

@media screen and (min-device-width: 100px) and (max-device-width: 767px) { 
    .sm-only-visible {
        
    }
}
@media screen and (min-device-width: 768px) and (max-device-width: 992px) { 
    .md-only-hidden {
        display: none !important
    }
}
