// Onest Thin
@font-face {
    font-family: 'Onest';
    font-style: normal;
    font-weight: 100;
    src: url(./../fonts/Onest-Thin.woff2) format("woff2");
}

// Onest ExtraLight
@font-face {
    font-family: 'Onest';
    font-style: normal;
    font-weight: 200;
    src: url(./../fonts/Onest-ExtraLight.woff2) format("woff2");
}

// Onest Light
@font-face {
    font-family: 'Onest';
    font-style: normal;
    font-weight: 300;
    src: url(./../fonts/Onest-Light.woff2) format("woff2");
}

// Onest Regular
@font-face {
    font-family: 'Onest';
    font-style: normal;
    font-weight: 400;
    src: url(./../fonts/Onest-Regular.woff2) format("woff2");
}

// Onest Medium
@font-face {
    font-family: 'Onest';
    font-style: normal;
    font-weight: 500;
    src: url(./../fonts/Onest-Medium.woff2) format("woff2");
}

// Onest SemiBold
@font-face {
    font-family: 'Onest';
    font-style: normal;
    font-weight: 600;
    src: url(./../fonts/Onest-SemiBold.woff2) format("woff2");
}

// Onest Bold
@font-face {
    font-family: 'Onest';
    font-style: normal;
    font-weight: 700;
    src: url(./../fonts/Onest-Bold.woff2) format("woff2");
}

// Onest ExtraBold
@font-face {
    font-family: 'Onest';
    font-style: normal;
    font-weight: 800;
    src: url(./../fonts/Onest-ExtraBold.woff2) format("woff2");
}

// Onest Black
@font-face {
    font-family: 'Onest';
    font-style: normal;
    font-weight: 900;
    src: url(./../fonts/Onest-Black.woff2) format("woff2");
}

// Backward compatibility alias
@font-face {
    font-family: mainfont;
    font-style: normal;
    font-weight: 400;
    src: local("mainfont"), url(./../fonts/Onest-Regular.woff2) format("woff2");
}