.header {
	position: relative; // Для позиционирования выпадающих меню
}

.main-header__section {
	position: relative; // Для позиционирования выпадающих меню относительно этой секции
}

.header__nav {
	@include tablet {
		display: none;
	}
}
.sub-header__section {
	border-bottom: .1rem solid var(--borders-color);
}
.sub-header {
	text-transform: uppercase;
	font-weight: var(--font-weight-semibold);
	font-size: 1.3rem;
	color: var(--text-color);
	padding: 1rem 0;
	

	& a {
		color: var(--text-color);
		transition: color 0.3s ease;
		cursor: pointer;
	}

	& a:hover {
		color: var(--accent-color);

		.sub-header__icon-svg {
			color: var(--accent-color);
		}
	}
}

.sub-header__socials {
	gap: 2rem;

	@media (max-width:900px) {
		gap: .5rem 2rem;
	}
	@media (min-width:1200px) {
		gap: 4rem;
	}
}

.sub-header__calculate-phone {
	gap: 2rem;
	justify-content: end;

	@media (max-width:900px) {
		gap: .5rem 2rem;
	}
	@media (min-width:1200px) {
		gap: 4rem;
	}
}

.sub-header__item {
	display: inline-flex;
	align-items: center;
	gap: 0.8rem;
	color: var(--text-color);
	transition: color 0.3s ease;

	&:hover {
		color: var(--accent-color);

		.sub-header__item-svg {
			color: var(--accent-color);
		}
	}
}

.sub-header__item-svg {
	width: 2rem;
	height: 2rem;
	color: var(--text-color);
	transition: color 0.3s ease;
	flex-shrink: 0;

	@media (max-width:460px) {
		display:none;
	}
}

@media (max-width:420px) {
	.sub-header__item-email,
	.sub-header__item-calculate {
		display: none;
	}
}

.phone {
	font-weight: var(--font-weight-bold);
}
.main-header__section {
	border-bottom: .1rem solid var(--borders-color);
}
.main-header {
	align-items: center;
	font-size: 1.3rem;
	color: var(--text-color);
	padding: 1rem 0;

}
.logo-descriptor {
	gap:3rem;
	margin-right: 3rem;

	@media (max-width:992px) {
		margin-right:0;
		justify-content: space-between;
		width: 100%;
	}
	@media (max-width:576px) {
		gap:5.5rem;
	}
}
.logo-and-descriptor {
	gap:3rem;
}
.logo {
	width: 17.2rem;
	height: 8.6rem;
}
.descriptor {
	text-transform: none;
	font-weight: var(--font-weight-regular);
	max-width: 11rem;
	font-size:1.3rem;
	color: var(--grey-text);
	padding-top: 1.1rem;

	// Скрываем дескриптор на мобильных устройствах (<768px)
	@media (max-width: 767px) {
		display: none;
	}
}










@media (min-width: 993px) {
	.main-menu {
		flex-grow:1;
	}
	.main-menu-btn-desktop {
		display: flex;
		flex-wrap: nowrap;
		color: var(--white-text);
		padding: 1.3rem 1.2rem;
		font-size: 1.2rem;
		font-weight: 600;
		text-transform: uppercase;
		border-radius: .7rem;
		background: #15a0e5 !important;
		height: 5rem;
		align-items: center;

		width:16rem;
		white-space: nowrap;

		@media (max-width: 992px) {
			display: none;
		}

		&:hover {
			background: linear-gradient(180deg, #3cb5f1 0%, #15a0e5 100%) !important;
			transition: background .3s ease;
		}
	}

	// Переопределение глобального сброса для кнопки
	button.main-menu-btn-desktop {
		background: #15a0e5 !important;
		background-color: #15a0e5 !important;

		&:hover {
			background: linear-gradient(180deg, #3cb5f1 0%, #15a0e5 100%) !important;
		}
	}

	.main-menu-btn-desktop svg {
		margin-right: 1rem;
		width: 2.4rem;
		height: 2.4rem;
		flex-shrink: 0;
	}
	.main-menu-btn-desktop svg path {
		transition-property: all;
		transition-delay: 0.1s;
		transition-duration: 0.6s;
		transition-timing-function: ease-out;
		stroke: white;
	}
	.main-menu-btn-desktop:hover svg path {
		transform: scaleX(1);
		transition-property: all;
		transition-delay: 0.1s;
		transition-duration: 0.6s;
		transition-timing-function: ease-out;
	}
	.main-menu-btn-desktop:hover svg path:nth-child(1) {
		transform: scaleX(1.2);
	}
	.main-menu-btn-desktop:hover svg path:nth-child(2) {
		transform: scaleX(1);
	}
	.main-menu-btn-desktop:hover svg path:nth-child(3) {
		transform: scaleX(1.3) translateX(.01rem);
	}
	.nav__btn-menu {
		display: flex;
		flex-direction: row;
		flex-wrap: nowrap;
		gap: 3rem;
		align-items: center;
	}

	.main-menu__list {
		display: flex;
		justify-content: space-between;
		width: 100%;
		

		& li:last-child {
			& a {
				padding: .5rem 0 .5rem .7rem !important;			
			}
		}
		@media (max-width:1279px) {
			justify-content: flex-start;
			flex-wrap:wrap;
		}
	}

	.main-menu__list li a {
		color: var(--black-text);
		font-size: 1.5rem;
		font-weight: 700;
		line-height: 2rem; /* 166.667% */
		text-transform: uppercase;
		display: inline-flex;
		padding: .5rem .7rem;
		justify-content: center;
		align-items: center;
		flex-shrink: 0;
		background: linear-gradient(180deg, rgba(224, 224, 224, 0.00) 0%, rgba(165, 163, 163, 0.00) 100%);
		white-space: nowrap;
		
	}
	.main-menu__list li a:hover {
		color: var(--accent-color);
	}
	.main-menu__list--icon a {
		display: flex;
		gap: 1rem;
	}
	.main-menu__list--icon a svg {
		fill:var(--headers__on-dark);
	}
	.main-menu__list--icon a:hover svg {
		fill:var(--bgr-color__dark-gray);
	}

	.main-menu-modal {
		background: var(--bgr-color__dark-gray-original);
		left: 0;
		right: 0;
		margin-top: 0.1rem;
		opacity: 0;
		position: absolute;
		top: 100%;
		visibility: collapse;
		width: auto;
		z-index: 10;
		border-bottom: 1px solid var(--borders__on-dark);
		font-size: 3.3rem;
		
	}

	.active .main-menu-modal {
		opacity: 1;
		visibility: visible;
		z-index: 10;
		-webkit-animation: menushow .5s cubic-bezier(.7, 0, .3, 1);
		animation: menushow .5s cubic-bezier(.7, 0, .3, 1);
	}
	@keyframes menushow {
		0% {
			-webkit-transform: translateY(-10%) scale(100%);
			transform: translateY(-10%) scale(100%);
			opacity: 0;
		}

		100% {
			-webkit-transform: translateY(0%);
			transform: translateY(0%);
			opacity: 100%;
		}
	}
	.main-menu__catalog-uslug {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 0;
	}
	.catalog-uslug__item {
		padding:3rem 3rem 3rem;
		display: flex;
		flex-direction: column;
		gap: 3rem;
		position: relative;
		overflow: hidden;
		overflow-y: scroll;
		height: 40vh;
	}
	.catalog-uslug__item:hover {
		animation: bgrcolor;
		animation-delay: .8s;
		animation-duration: 0ms;
		animation-fill-mode: forwards;
	}
	@keyframes bgrcolor {
		0% {
			background-color: unset;
			
		}
		100% {
			background-color: var(--bgr-color__dark-gray);
		}
	}

	.catalog-uslug__h3 {
		z-index: 3;
		color: #FFF; 
		mix-blend-mode: difference;
		font-size: 2rem;
		font-weight: 700;
		line-height: 140%;
	}
	.catalog-uslug__item img {
		z-index: 3;
		border-radius: 10rem;
		width: 100%;
		object-fit: cover;
	}
	.catalog-menu__list {
		z-index: 3;
		font-size: 1.4rem;
		line-height: 140%;
		list-style: disc;
		align-self: flex-start;
		display: none;
	}
	.catalog-uslug__item:hover .catalog-menu__list {
		display: block;
		animation: catmenulist-in .6s cubic-bezier(.7, 0, .3, 1);
		animation-fill-mode: forwards;
	}
	@keyframes catmenulist-in {
		0% {
			-webkit-transform: translateY(200%);
			transform: translateY(200%);
			opacity: 0%;
			display: none;
		}

		100% {
			-webkit-transform: translateY(0%) scale(100%);
			transform: translateY(0%) scale(100%);
			opacity: 100%;
			display: block;
		}
	}
	.catalog-menu__list li {
		list-style: circle;
		list-style-position: outside;
		margin-left: 1.5rem;
		list-style-type: disc;
		color:var(--white);
		padding-bottom: 1.2rem;
	}
	.catalog-menu__list li:last-child {
		padding-bottom: 0;
	}
	.catalog-menu__list a {
		color:var(--text__on-dark);
	}
	.catalog-menu__list a:hover {
		text-decoration: underline;
	}
	.catalog-uslug__item:hover .catalog-uslug__h3 {
	}
	.catalog-uslug__item img {
		animation: catmenuimg-in .8s cubic-bezier(.7, 0, .3, 1);
		animation-fill-mode: forwards;
	}
	.catalog-uslug__item:hover img {
		animation: catmenuimg-out .8s cubic-bezier(.7, 0, .3, 1);
		animation-fill-mode: forwards;
	}
	@keyframes catmenuimg-out {
		0% {
			-webkit-transform: translateY(0%) scale(100%);
			transform: translateY(0%) scale(100%);
			opacity: 100%;
			display: block;
		}

		100% {
			-webkit-transform: translateY(200%);
			transform: translateY(200%);
			opacity: 0%;
			display: none;
		}
	}
	@keyframes catmenuimg-in {
		0% {
			-webkit-transform: translateY(200%);
			transform: translateY(200%);
			opacity: 0%;
			display: none;
		}

		100% {
			-webkit-transform: translateY(0%) scale(100%);
			transform: translateY(0%) scale(100%);
			opacity: 100%;
			display: block;
		}
	}
	.catalog-uslug__item::before {
		content: " ";
		width: 100%;
		height: 0;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: var(--bgr-color__dark-gray);
		border-radius: inherit;
		transition: 0.8s;
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		margin: auto;
		z-index: 2;
	}
	.catalog-uslug__item:hover:before {
		width: 100%;
		height: 100%;
	}
	.catalog-uslug__item:nth-child(1),
	.catalog-uslug__item:nth-child(2),
	.catalog-uslug__item:nth-child(3){
		border-right: 1px solid var(--borders__on-dark);
		border-bottom: 1px solid var(--borders__on-dark);
	}
	.catalog-uslug__item:nth-child(4){
		border-bottom: 1px solid var(--borders__on-dark);
	}
	.catalog-uslug__item:nth-child(5),
	.catalog-uslug__item:nth-child(6),
	.catalog-uslug__item:nth-child(7){
		border-right: 1px solid var(--borders__on-dark);
	}
	.catalog-uslug__item:nth-child(8){
		// border-bottom: 1px solid var(--borders__on-dark);
	}
}

@media (max-width: 992px) {
	.main-menu {
		flex-direction: column;
		-webkit-box-orient: vertical;
		-webkit-box-direction: reverse;
		-webkit-box-pack: end;
		-ms-flex-pack: end;
		background: var(--menu-color);
		-webkit-box-shadow: 0 8px 9px 0 rgba(0, 0, 0, .07);
		box-shadow: 0 8px 9px 0 rgba(0, 0, 0, .07);
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		gap: 2rem;
		height: 100vh;
		justify-content: flex-start;
		left: 0;
		padding-top: 2rem;
		position: absolute;
		top: 0;
		-webkit-transform: translateX(-120%);
		-ms-transform: translateX(-120%);
		transform: translateX(-120%);
		-webkit-transition: .2s;
		transition: .2s;
		width: 100%;
		z-index: 97;
		overflow-y: scroll;
		
	}
	.main-menu__list {
		font-size: 1.4rem;
		margin-bottom: 3rem;
		margin: auto 0;
        padding: 0 1.6rem;
	}
	.main-menu__list a {
		color: var(--black-text);
        display: flex;
        gap: 0.5rem;
        font-size: 1.7rem;
        padding: 1.5rem 0;
        border-bottom: 1px solid var(--borders-color);

		&:hover, &:focus {
			padding-left: 1.5rem;
			color: var(--accent-color);
		}

		&:active {
			color: var(--accent-color);
		}
	}
	.main-menu__list a svg {
		fill:var(--black-text);
		width: 1.6rem;
    	height: 1.6rem;
	}
	.main-menu__list li {
		transition: linear .4s;
	}
	.main-menu__list li:last-child {
		padding-bottom: 0;
	}
	.main-menu__catalog-uslug {
		padding-bottom: 2rem;
	}
	.main-menu__list::before,
	.main-menu__catalog-uslug::before {
        content: attr(data-name) "";
        font-size: 1.6rem;
        padding: 2rem 0;
        color: var(--accent-color);
        display: block;
        text-transform: uppercase;
        font-weight: var(--font-weight-semibold);
	}
	.main-menu__list--icon a {
		display: flex;
		gap: .5rem;
	}
	.main-menu__list--icon a svg {
		fill:var(--headers__on-dark);
		width: 1.6rem;
    	height: 1.6rem;
	}
	.sm-disable-margin {
		margin: auto 0;
	}

	.catalog-uslug__item {
        position: relative;
        -webkit-transition: .4s;
        -o-transition: .4s;
        transition: .4s;
		display: grid;
		grid-template-columns: 1fr;
		grid-template-rows: auto;
		grid-column-gap: 0px;
		grid-row-gap: 1rem;
		align-items: center;
		padding: 0.7rem 0;
		line-height: 3rem;
    }
	.catalog-uslug__item h3 {
		padding-left: 1rem;
		font-size: 1.4rem;
		line-height: 140%;
	}
	.catalog-uslug__item ul {
		grid-area: 2 / 1 / 3 / 3;
	}
	.catalog-uslug__item img {
		grid-area: 1 / 1 / 2 / 2;
	}

    .catalog-uslug__item.active {
        background: var(--bgr-color__dark-gray-original)
    }

    .catalog-uslug__item.active .accordeon-list-btn {
        font-weight: 700
    }

    .catalog-uslug__item.active .accordeon-list-btn:after {
        -webkit-box-shadow: inset 0 0 0 .1rem #c3beab;
        box-shadow: inset 0 0 0 .1rem #c3beab;
        -webkit-transform: translateY(-50%) rotate(-180deg);
        -ms-transform: translateY(-50%) rotate(-180deg);
        transform: translateY(-50%) rotate(-180deg)
    }

    .catalog-uslug__item:not(:last-child) {
        border-bottom: .1rem solid var(--borders__on-dark)
    }

    .catalog-uslug__item ul {
        display: none;
        font-size: 1rem;
        list-style: none;
        margin: 0 0 1.5rem 1.5rem;
        padding: 0
    }

    .catalog-uslug__item ul li {
		display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        padding: .75rem .8rem;
		margin-right: 1.5rem;
		font-size: 1.4rem;
    }
	.catalog-uslug__item ul li a {
		color: var(--text__on-dark);
		line-height: 140%;
	}

    .catalog-uslug__item ul li:not(:last-child) {
        border-bottom: .1rem solid var(--borders__on-dark);
    }

    .accordeon-list-btn,
    .catalog-uslug__item ul a {
        text-decoration: none
    }
	.catalog-uslug__item img {
		width: 100%;
		border-radius: 5rem;
		height: 100%;
		object-fit: cover;
	}
	.catalog-uslug__item ul li {
		padding: 1.5rem 0;
	}
    .catalog-uslug__h3 {
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        border: none;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        font-size: 1.2rem;
        padding: .7rem 2.4rem;
        pointer-events: none;
        position: relative;
        width: 100%
    }

    .catalog-uslug__h3:focus,
    .catalog-uslug__h3:hover {
        color: #2b2e35
    }

    .catalog-uslug__h3:after {
        background: url(./../img/arrow-down.svg) no-repeat 50%;
        background-size: contain;
        border-radius: .5rem;
        content: "";
        display: block;
        height: 1.6rem;
        position: absolute;
        right: .8rem;
        top: 50%;
        -webkit-transform: translateY(-50%) rotate(0deg);
        -ms-transform: translateY(-50%) rotate(0deg);
        transform: translateY(-50%) rotate(0deg);
        -webkit-transition: .4s;
        -o-transition: .4s;
        transition: .4s;
        width: 1.6rem
    }
	.catalog-uslug__item.active .catalog-uslug__h3:after {
		-webkit-transform: translateY(-50%) rotate(-180deg);
		-ms-transform: translateY(-50%) rotate(-180deg);
		transform: translateY(-50%) rotate(-180deg);
	}

	// Стили для пункта-ссылки
	.catalog-uslug__item--link {
		// Ссылка выглядит как обычный заголовок h3
		.catalog-uslug__link.catalog-uslug__h3 {
			padding-left: 1rem;
			font-size: 1.4rem;
			line-height: 140%;
		}

		// Убираем стрелку для пункта-ссылки
		.catalog-uslug__link.catalog-uslug__h3:after {
			display: none;
		}

		// Эффекты при наведении
		&:hover .catalog-uslug__link.catalog-uslug__h3 {
			color: var(--accent-color);
		}
	}
	.top-header {
		margin: 0 -2rem;
	}
	.top-header__logo {
		max-width: 50%;
		margin-right: auto;
    	padding-left: 2rem;
	}
	.header {
		width: 100%;
	}
	.mobil-menu__info {
		margin-top:2rem;
		margin-bottom: 4rem;
	}
	.top-header__contacts--grafic {
		padding-top: 0.6rem;
		text-align: center;
		padding-bottom: 2rem;
	}
	.top-header__messengers {
		margin-right: 3rem;
	}
	.top-header__messengers__icons {
		gap: 3rem;
		justify-content: center;
	}
}
@media (max-width: 576px) {
	.top-header {
		margin: 0 -1.6rem;
	}
}
.main-menu-btn {
    background: var(--menu-color);
	border-radius: .7rem;
    border: .125rem solid var(--borders-color);
    height: 7.4rem;
    overflow: hidden;
    padding: 0;
    position: relative;
    -webkit-transition: .6s;
    transition: .6s;
    width: 7.4rem;
    z-index: 99
}

.main-menu-btn:hover {
    background: var(--accent-color);
	border: none;
}

.main-menu-btn .open {
    height: 100%;
    left: 0;
    opacity: 1;
    padding: 2rem;
    position: absolute;
    top: 0;
    width: 100%;
    z-index: 1
}

.main-menu-open .main-menu-btn .open {
    opacity: 0;
    z-index: -1
}

.main-menu-btn .open span {
    background: var(--black-text);
    display: block;
    height: .25rem;
    position: relative;
    -webkit-transition: .6s;
    transition: .6s;
    width: 100%;
	border-radius: 0.7rem;
}

.main-menu-btn .open span+span {
    margin-top: .75rem;
    width: 2.8rem
}
.main-menu-btn .open span+span+span {
    margin-top: .75rem;
    width: 2.2rem
}

.main-menu-btn .open:after {
    bottom: 1rem;
    color: var(--black-text);
    content: attr(data-attr);
    display: block;
    font-size: 1rem;
    font-weight: 700;
    left: 50%;
    line-height: 1.2;
    opacity: 1;
    position: absolute;
    text-transform: uppercase;
    -webkit-transform: translate(-50%);
    -ms-transform: translate(-50%);
    transform: translate(-50%);
    -webkit-transition: .6s;
    transition: .6s;
    width: 5rem
}

.main-menu-btn .close {
    color: var(--black-text);
    height: 100%;
    left: 0;
    opacity: 0;
    position: absolute;
    top: 0;
    width: 100%;
    z-index: -1
}

.main-menu-btn .close svg {
    height: 2.7rem;
    left: 50%;
    -o-object-fit: cover;
    object-fit: cover;
    position: absolute;
    top: 50%;
    -webkit-transform: translate(-50%, -50%) rotate(0);
    -ms-transform: translate(-50%, -50%) rotate(0);
    transform: translate(-50%, -50%) rotate(0);
    -webkit-transition: .6s;
    transition: .6s;
    width: 2.7rem
}

.main-menu-btn .close:after {
    bottom: -2rem;
    color: var(--bgr-color__dark-gray);
    content: attr(data-attr);
    display: block;
    font-size: .8rem;
    font-weight: 700;
    left: 50%;
    line-height: 1.2;
    opacity: 0;
    position: absolute;
    text-transform: uppercase;
    -webkit-transform: translate(-50%);
    -ms-transform: translate(-50%);
    transform: translate(-50%);
    -webkit-transition: .6s;
    transition: .6s;
    width: 5rem
}

.main-menu-btn:hover .open span {
    background: #404246;
    -webkit-transform: translateY(-.1rem);
    -ms-transform: translateY(-.1rem);
    transform: translateY(-.1rem)
}

.main-menu-btn:hover .open span+span {
    width: 100%
}

.main-menu-btn:hover .open:after {
    bottom: 1.4rem;
    opacity: 1
}

.main-menu-open .main-menu-btn .close {
    opacity: 1;
    z-index: 1
}

.main-menu-open .main-menu-btn:hover .close {
    color: var(--bgr-color__dark-gray)
}

.main-menu-open .main-menu-btn:hover .close svg {
    -webkit-transform: translate(-50%, calc(-50% - 1rem)) rotate(-180deg);
    -ms-transform: translate(-50%, calc(-50% - 1rem)) rotate(-180deg);
    transform: translate(-50%, calc(-50% - 1rem)) rotate(-180deg)
}

.main-menu-open .main-menu-btn:hover .close:after {
    bottom: 1.4rem;
    opacity: 1;
    visibility: visible
}

.main-menu-open .main-menu {
    -webkit-transform: translateX(0);
    -ms-transform: translateX(0);
    transform: translateX(0);
}

@media only screen and (min-width:993px) {
	.main-menu-btn {
		display: none
	}
}



@media only screen and (max-width:992px) {
	.body-hidden {
        overflow: hidden
    }
}
@media only screen and (max-width:1400px) {
	.hide-on-1400 {
		display: none;
	}
}