/*! For license information please see index.bundle.js.LICENSE.txt */
!function(){var t={583:function(t,e,n){"use strict";var i=n(81),o=n.n(i),s=n(645),r=n.n(s)()(o());r.push([t.id,':root{--f-spinner-width: 36px;--f-spinner-height: 36px;--f-spinner-color-1: rgba(0, 0, 0, 0.1);--f-spinner-color-2: rgba(17, 24, 28, 0.8);--f-spinner-stroke: 2.75}.f-spinner{margin:auto;padding:0;width:var(--f-spinner-width);height:var(--f-spinner-height)}.f-spinner svg{width:100%;height:100%;vertical-align:top;animation:f-spinner-rotate 2s linear infinite}.f-spinner svg *{stroke-width:var(--f-spinner-stroke);fill:none}.f-spinner svg *:first-child{stroke:var(--f-spinner-color-1)}.f-spinner svg *:last-child{stroke:var(--f-spinner-color-2);animation:f-spinner-dash 2s ease-in-out infinite}@keyframes f-spinner-rotate{100%{transform:rotate(360deg)}}@keyframes f-spinner-dash{0%{stroke-dasharray:1,150;stroke-dashoffset:0}50%{stroke-dasharray:90,150;stroke-dashoffset:-35}100%{stroke-dasharray:90,150;stroke-dashoffset:-124}}.f-throwOutUp{animation:var(--f-throw-out-duration, 0.175s) ease-out both f-throwOutUp}.f-throwOutDown{animation:var(--f-throw-out-duration, 0.175s) ease-out both f-throwOutDown}@keyframes f-throwOutUp{to{transform:translate3d(0, calc(var(--f-throw-out-distance, 150px) * -1), 0);opacity:0}}@keyframes f-throwOutDown{to{transform:translate3d(0, var(--f-throw-out-distance, 150px), 0);opacity:0}}.f-zoomInUp{animation:var(--f-transition-duration, 0.2s) ease .1s both f-zoomInUp}.f-zoomOutDown{animation:var(--f-transition-duration, 0.2s) ease both f-zoomOutDown}@keyframes f-zoomInUp{from{transform:scale(0.975) translate3d(0, 16px, 0);opacity:0}to{transform:scale(1) translate3d(0, 0, 0);opacity:1}}@keyframes f-zoomOutDown{to{transform:scale(0.975) translate3d(0, 16px, 0);opacity:0}}.f-fadeIn{animation:var(--f-transition-duration, 0.2s) var(--f-transition-easing, ease) var(--f-transition-delay, 0s) both f-fadeIn;z-index:2}.f-fadeOut{animation:var(--f-transition-duration, 0.2s) var(--f-transition-easing, ease) var(--f-transition-delay, 0s) both f-fadeOut;z-index:1}@keyframes f-fadeIn{0%{opacity:0}100%{opacity:1}}@keyframes f-fadeOut{100%{opacity:0}}.f-fadeFastIn{animation:var(--f-transition-duration, 0.2s) ease-out both f-fadeFastIn;z-index:2}.f-fadeFastOut{animation:var(--f-transition-duration, 0.1s) ease-out both f-fadeFastOut;z-index:2}@keyframes f-fadeFastIn{0%{opacity:.75}100%{opacity:1}}@keyframes f-fadeFastOut{100%{opacity:0}}.f-fadeSlowIn{animation:var(--f-transition-duration, 0.5s) ease both f-fadeSlowIn;z-index:2}.f-fadeSlowOut{animation:var(--f-transition-duration, 0.5s) ease both f-fadeSlowOut;z-index:1}@keyframes f-fadeSlowIn{0%{opacity:0}100%{opacity:1}}@keyframes f-fadeSlowOut{100%{opacity:0}}.f-crossfadeIn{animation:var(--f-transition-duration, 0.2s) ease-out both f-crossfadeIn;z-index:2}.f-crossfadeOut{animation:calc(var(--f-transition-duration, 0.2s)*.5) linear .1s both f-crossfadeOut;z-index:1}@keyframes f-crossfadeIn{0%{opacity:0}100%{opacity:1}}@keyframes f-crossfadeOut{100%{opacity:0}}.f-slideIn.from-next{animation:var(--f-transition-duration, 0.85s) cubic-bezier(0.16, 1, 0.3, 1) f-slideInNext}.f-slideIn.from-prev{animation:var(--f-transition-duration, 0.85s) cubic-bezier(0.16, 1, 0.3, 1) f-slideInPrev}.f-slideOut.to-next{animation:var(--f-transition-duration, 0.85s) cubic-bezier(0.16, 1, 0.3, 1) f-slideOutNext}.f-slideOut.to-prev{animation:var(--f-transition-duration, 0.85s) cubic-bezier(0.16, 1, 0.3, 1) f-slideOutPrev}@keyframes f-slideInPrev{0%{transform:translateX(100%)}100%{transform:translate3d(0, 0, 0)}}@keyframes f-slideInNext{0%{transform:translateX(-100%)}100%{transform:translate3d(0, 0, 0)}}@keyframes f-slideOutNext{100%{transform:translateX(-100%)}}@keyframes f-slideOutPrev{100%{transform:translateX(100%)}}.f-classicIn.from-next{animation:var(--f-transition-duration, 0.85s) cubic-bezier(0.16, 1, 0.3, 1) f-classicInNext;z-index:2}.f-classicIn.from-prev{animation:var(--f-transition-duration, 0.85s) cubic-bezier(0.16, 1, 0.3, 1) f-classicInPrev;z-index:2}.f-classicOut.to-next{animation:var(--f-transition-duration, 0.85s) cubic-bezier(0.16, 1, 0.3, 1) f-classicOutNext;z-index:1}.f-classicOut.to-prev{animation:var(--f-transition-duration, 0.85s) cubic-bezier(0.16, 1, 0.3, 1) f-classicOutPrev;z-index:1}@keyframes f-classicInNext{0%{transform:translateX(-75px);opacity:0}100%{transform:translate3d(0, 0, 0);opacity:1}}@keyframes f-classicInPrev{0%{transform:translateX(75px);opacity:0}100%{transform:translate3d(0, 0, 0);opacity:1}}@keyframes f-classicOutNext{100%{transform:translateX(-75px);opacity:0}}@keyframes f-classicOutPrev{100%{transform:translateX(75px);opacity:0}}:root{--f-button-width: 40px;--f-button-height: 40px;--f-button-border: 0;--f-button-border-radius: 0;--f-button-color: #374151;--f-button-bg: #f8f8f8;--f-button-hover-bg: #e0e0e0;--f-button-active-bg: #d0d0d0;--f-button-shadow: none;--f-button-transition: all 0.15s ease;--f-button-transform: none;--f-button-svg-width: 20px;--f-button-svg-height: 20px;--f-button-svg-stroke-width: 1.5;--f-button-svg-fill: none;--f-button-svg-filter: none;--f-button-svg-disabled-opacity: 0.65}.f-button{display:flex;justify-content:center;align-items:center;box-sizing:content-box;position:relative;margin:0;padding:0;width:var(--f-button-width);height:var(--f-button-height);border:var(--f-button-border);border-radius:var(--f-button-border-radius);color:var(--f-button-color);background:var(--f-button-bg);box-shadow:var(--f-button-shadow);pointer-events:all;cursor:pointer;transition:var(--f-button-transition)}@media(hover: hover){.f-button:hover:not([disabled]){color:var(--f-button-hover-color);background-color:var(--f-button-hover-bg)}}.f-button:active:not([disabled]){background-color:var(--f-button-active-bg)}.f-button:focus:not(:focus-visible){outline:none}.f-button:focus-visible{outline:none;box-shadow:inset 0 0 0 var(--f-button-outline, 2px) var(--f-button-outline-color, var(--f-button-color))}.f-button svg{width:var(--f-button-svg-width);height:var(--f-button-svg-height);fill:var(--f-button-svg-fill);stroke:currentColor;stroke-width:var(--f-button-svg-stroke-width);stroke-linecap:round;stroke-linejoin:round;transition:opacity .15s ease;transform:var(--f-button-transform);filter:var(--f-button-svg-filter);pointer-events:none}.f-button[disabled]{cursor:default}.f-button[disabled] svg{opacity:var(--f-button-svg-disabled-opacity)}.f-carousel__nav .f-button.is-prev,.f-carousel__nav .f-button.is-next,.fancybox__nav .f-button.is-prev,.fancybox__nav .f-button.is-next{position:absolute;z-index:1}.is-horizontal .f-carousel__nav .f-button.is-prev,.is-horizontal .f-carousel__nav .f-button.is-next,.is-horizontal .fancybox__nav .f-button.is-prev,.is-horizontal .fancybox__nav .f-button.is-next{top:50%;transform:translateY(-50%)}.is-horizontal .f-carousel__nav .f-button.is-prev,.is-horizontal .fancybox__nav .f-button.is-prev{left:var(--f-button-prev-pos)}.is-horizontal .f-carousel__nav .f-button.is-next,.is-horizontal .fancybox__nav .f-button.is-next{right:var(--f-button-next-pos)}.is-horizontal.is-rtl .f-carousel__nav .f-button.is-prev,.is-horizontal.is-rtl .fancybox__nav .f-button.is-prev{left:auto;right:var(--f-button-next-pos)}.is-horizontal.is-rtl .f-carousel__nav .f-button.is-next,.is-horizontal.is-rtl .fancybox__nav .f-button.is-next{right:auto;left:var(--f-button-prev-pos)}.is-vertical .f-carousel__nav .f-button.is-prev,.is-vertical .f-carousel__nav .f-button.is-next,.is-vertical .fancybox__nav .f-button.is-prev,.is-vertical .fancybox__nav .f-button.is-next{top:auto;left:50%;transform:translateX(-50%)}.is-vertical .f-carousel__nav .f-button.is-prev,.is-vertical .fancybox__nav .f-button.is-prev{top:var(--f-button-next-pos)}.is-vertical .f-carousel__nav .f-button.is-next,.is-vertical .fancybox__nav .f-button.is-next{bottom:var(--f-button-next-pos)}.is-vertical .f-carousel__nav .f-button.is-prev svg,.is-vertical .f-carousel__nav .f-button.is-next svg,.is-vertical .fancybox__nav .f-button.is-prev svg,.is-vertical .fancybox__nav .f-button.is-next svg{transform:rotate(90deg)}.f-carousel__nav .f-button:disabled,.fancybox__nav .f-button:disabled{pointer-events:none}html.with-fancybox{width:auto;overflow:visible;scroll-behavior:auto}html.with-fancybox body{touch-action:none}html.with-fancybox body.hide-scrollbar{width:auto;margin-right:calc(var(--fancybox-body-margin, 0px) + var(--fancybox-scrollbar-compensate, 0px));overflow:hidden !important;overscroll-behavior-y:none}.fancybox__container{--fancybox-color: #dbdbdb;--fancybox-hover-color: #fff;--fancybox-bg: rgba(24, 24, 27, 0.98);--fancybox-slide-gap: 10px;--f-spinner-width: 50px;--f-spinner-height: 50px;--f-spinner-color-1: rgba(255, 255, 255, 0.1);--f-spinner-color-2: #bbb;--f-spinner-stroke: 3.65;position:fixed;top:0;left:0;bottom:0;right:0;direction:ltr;display:flex;flex-direction:column;box-sizing:border-box;margin:0;padding:0;color:#f8f8f8;-webkit-tap-highlight-color:rgba(0,0,0,0);overflow:visible;z-index:var(--fancybox-zIndex, 1050);outline:none;transform-origin:top left;-webkit-text-size-adjust:100%;-moz-text-size-adjust:none;-ms-text-size-adjust:100%;text-size-adjust:100%;overscroll-behavior-y:contain}.fancybox__container *,.fancybox__container *::before,.fancybox__container *::after{box-sizing:inherit}.fancybox__container *:empty{display:block}.fancybox__container::backdrop{background-color:rgba(0,0,0,0)}.fancybox__backdrop{position:fixed;top:0;left:0;bottom:0;right:0;z-index:-1;background:var(--fancybox-bg);opacity:var(--fancybox-opacity, 1);will-change:opacity}.fancybox__carousel{position:relative;box-sizing:border-box;flex:1;min-height:0;z-index:10;overflow-y:visible;overflow-x:clip}.fancybox__viewport{width:100%;height:100%}.fancybox__viewport.is-draggable{cursor:move;cursor:grab}.fancybox__viewport.is-dragging{cursor:move;cursor:grabbing}.fancybox__track{display:flex;margin:0 auto;height:100%}.fancybox__slide{flex:0 0 auto;position:relative;display:flex;flex-direction:column;align-items:center;width:100%;height:100%;margin:0 var(--fancybox-slide-gap) 0 0;padding:4px;overflow:auto;overscroll-behavior:contain;transform:translate3d(0, 0, 0);backface-visibility:hidden}.fancybox__container:not(.is-compact) .fancybox__slide.has-close-btn{padding-top:40px}.fancybox__slide.has-iframe,.fancybox__slide.has-video,.fancybox__slide.has-html5video{overflow:hidden}.fancybox__slide.has-image{overflow:hidden}.fancybox__slide.has-image.is-animating,.fancybox__slide.has-image.is-selected{overflow:visible}.fancybox__slide::before,.fancybox__slide::after{content:"";flex:0 0 0;margin:auto}.fancybox__content{align-self:center;display:flex;flex-direction:column;position:relative;margin:0;padding:2rem;max-width:100%;color:var(--fancybox-content-color, #374151);background:var(--fancybox-content-bg, #fff);cursor:default;border-radius:0;z-index:20}.is-loading .fancybox__content{opacity:0}.is-draggable .fancybox__content{cursor:move;cursor:grab}.can-zoom_in .fancybox__content{cursor:zoom-in}.can-zoom_out .fancybox__content{cursor:zoom-out}.is-dragging .fancybox__content{cursor:move;cursor:grabbing}.fancybox__content [data-selectable],.fancybox__content [contenteditable]{cursor:auto}.fancybox__slide.has-image>.fancybox__content{padding:0;background:rgba(0,0,0,0);min-height:1px;background-repeat:no-repeat;background-size:contain;background-position:center center;transition:none;transform:translate3d(0, 0, 0);backface-visibility:hidden}.fancybox__slide.has-image>.fancybox__content>picture>img{width:100%;height:auto;max-height:100%}.is-animating .fancybox__content,.is-dragging .fancybox__content{will-change:transform,width,height}.fancybox-image{margin:auto;display:block;width:100%;height:100%;min-height:0;object-fit:contain;user-select:none;filter:blur(0px)}.fancybox__caption{align-self:center;max-width:100%;flex-shrink:0;margin:0;padding:14px 0 4px 0;overflow-wrap:anywhere;line-height:1.375;color:var(--fancybox-color, currentColor);opacity:var(--fancybox-opacity, 1);cursor:auto;visibility:visible}.is-loading .fancybox__caption,.is-closing .fancybox__caption{opacity:0;visibility:hidden}.is-compact .fancybox__caption{padding-bottom:0}.f-button.is-close-btn{--f-button-svg-stroke-width: 2;position:absolute;top:0;right:8px;z-index:40}.fancybox__content>.f-button.is-close-btn{--f-button-width: 34px;--f-button-height: 34px;--f-button-border-radius: 4px;--f-button-color: var(--fancybox-color, #fff);--f-button-hover-color: var(--fancybox-color, #fff);--f-button-bg: transparent;--f-button-hover-bg: transparent;--f-button-active-bg: transparent;--f-button-svg-width: 22px;--f-button-svg-height: 22px;position:absolute;top:-38px;right:0;opacity:.75}.is-loading .fancybox__content>.f-button.is-close-btn{visibility:hidden}.is-zooming-out .fancybox__content>.f-button.is-close-btn{visibility:hidden}.fancybox__content>.f-button.is-close-btn:hover{opacity:1}.fancybox__footer{padding:0;margin:0;position:relative}.fancybox__footer .fancybox__caption{width:100%;padding:24px;opacity:var(--fancybox-opacity, 1);transition:all .25s ease}.is-compact .fancybox__footer{position:absolute;bottom:0;left:0;right:0;z-index:20;background:rgba(24,24,27,.5)}.is-compact .fancybox__footer .fancybox__caption{padding:12px}.is-compact .fancybox__content>.f-button.is-close-btn{--f-button-border-radius: 50%;--f-button-color: #fff;--f-button-hover-color: #fff;--f-button-outline-color: #000;--f-button-bg: rgba(0, 0, 0, 0.6);--f-button-active-bg: rgba(0, 0, 0, 0.6);--f-button-hover-bg: rgba(0, 0, 0, 0.6);--f-button-svg-width: 18px;--f-button-svg-height: 18px;--f-button-svg-filter: none;top:5px;right:5px}.fancybox__nav{--f-button-width: 50px;--f-button-height: 50px;--f-button-border: 0;--f-button-border-radius: 50%;--f-button-color: var(--fancybox-color);--f-button-hover-color: var(--fancybox-hover-color);--f-button-bg: transparent;--f-button-hover-bg: rgba(24, 24, 27, 0.3);--f-button-active-bg: rgba(24, 24, 27, 0.5);--f-button-shadow: none;--f-button-transition: all 0.15s ease;--f-button-transform: none;--f-button-svg-width: 26px;--f-button-svg-height: 26px;--f-button-svg-stroke-width: 2.5;--f-button-svg-fill: none;--f-button-svg-filter: drop-shadow(1px 1px 1px rgba(24, 24, 27, 0.5));--f-button-svg-disabled-opacity: 0.65;--f-button-next-pos: 1rem;--f-button-prev-pos: 1rem;opacity:var(--fancybox-opacity, 1)}.fancybox__nav .f-button:before{position:absolute;content:"";top:-30px;right:-20px;left:-20px;bottom:-30px;z-index:1}.is-idle .fancybox__nav{animation:.15s ease-out both f-fadeOut}.is-idle.is-compact .fancybox__footer{pointer-events:none;animation:.15s ease-out both f-fadeOut}.fancybox__slide>.f-spinner{position:absolute;top:50%;left:50%;margin:var(--f-spinner-top, calc(var(--f-spinner-width) * -0.5)) 0 0 var(--f-spinner-left, calc(var(--f-spinner-height) * -0.5));z-index:30;cursor:pointer}.fancybox-protected{position:absolute;top:0;left:0;right:0;bottom:0;z-index:40;user-select:none}.fancybox-ghost{position:absolute;top:0;left:0;width:100%;height:100%;min-height:0;object-fit:contain;z-index:40;user-select:none;pointer-events:none}.fancybox-focus-guard{outline:none;opacity:0;position:fixed;pointer-events:none}.fancybox__container:not([aria-hidden]){opacity:0}.fancybox__container.is-animated[aria-hidden=false]>*:not(.fancybox__backdrop,.fancybox__carousel),.fancybox__container.is-animated[aria-hidden=false] .fancybox__carousel>*:not(.fancybox__viewport),.fancybox__container.is-animated[aria-hidden=false] .fancybox__slide>*:not(.fancybox__content){animation:var(--f-interface-enter-duration, 0.25s) ease .1s backwards f-fadeIn}.fancybox__container.is-animated[aria-hidden=false] .fancybox__backdrop{animation:var(--f-backdrop-enter-duration, 0.35s) ease backwards f-fadeIn}.fancybox__container.is-animated[aria-hidden=true]>*:not(.fancybox__backdrop,.fancybox__carousel),.fancybox__container.is-animated[aria-hidden=true] .fancybox__carousel>*:not(.fancybox__viewport),.fancybox__container.is-animated[aria-hidden=true] .fancybox__slide>*:not(.fancybox__content){animation:var(--f-interface-exit-duration, 0.15s) ease forwards f-fadeOut}.fancybox__container.is-animated[aria-hidden=true] .fancybox__backdrop{animation:var(--f-backdrop-exit-duration, 0.35s) ease forwards f-fadeOut}.has-iframe .fancybox__content,.has-map .fancybox__content,.has-pdf .fancybox__content,.has-youtube .fancybox__content,.has-vimeo .fancybox__content,.has-html5video .fancybox__content{max-width:100%;flex-shrink:1;min-height:1px;overflow:visible}.has-iframe .fancybox__content,.has-map .fancybox__content,.has-pdf .fancybox__content{width:calc(100% - 120px);height:90%}.fancybox__container.is-compact .has-iframe .fancybox__content,.fancybox__container.is-compact .has-map .fancybox__content,.fancybox__container.is-compact .has-pdf .fancybox__content{width:100%;height:100%}.has-youtube .fancybox__content,.has-vimeo .fancybox__content,.has-html5video .fancybox__content{width:960px;height:540px;max-width:100%;max-height:100%}.has-map .fancybox__content,.has-pdf .fancybox__content,.has-youtube .fancybox__content,.has-vimeo .fancybox__content,.has-html5video .fancybox__content{padding:0;background:rgba(24,24,27,.9);color:#fff}.has-map .fancybox__content{background:#e5e3df}.fancybox__html5video,.fancybox__iframe{border:0;display:block;height:100%;width:100%;background:rgba(0,0,0,0)}.fancybox-placeholder{border:0 !important;clip:rect(1px, 1px, 1px, 1px) !important;-webkit-clip-path:inset(50%) !important;clip-path:inset(50%) !important;height:1px !important;margin:-1px !important;overflow:hidden !important;padding:0 !important;position:absolute !important;width:1px !important;white-space:nowrap !important}.f-carousel__thumbs{--f-thumb-width: 96px;--f-thumb-height: 72px;--f-thumb-outline: 0;--f-thumb-outline-color: #5eb0ef;--f-thumb-opacity: 1;--f-thumb-hover-opacity: 1;--f-thumb-selected-opacity: 1;--f-thumb-border-radius: 2px;--f-thumb-offset: 0px;--f-button-next-pos: 0;--f-button-prev-pos: 0}.f-carousel__thumbs.is-classic{--f-thumb-gap: 8px;--f-thumb-opacity: 0.5;--f-thumb-hover-opacity: 1;--f-thumb-selected-opacity: 1}.f-carousel__thumbs.is-modern{--f-thumb-gap: 4px;--f-thumb-extra-gap: 16px;--f-thumb-clip-width: 46px}.f-thumbs{position:relative;flex:0 0 auto;margin:0;overflow:hidden;-webkit-tap-highlight-color:rgba(0,0,0,0);user-select:none;perspective:1000px;transform:translateZ(0)}.f-thumbs .f-spinner{position:absolute;top:0;left:0;width:100%;height:100%;border-radius:2px;background-image:linear-gradient(#ebeff2, #e2e8f0);z-index:-1}.f-thumbs .f-spinner svg{display:none}.f-thumbs.is-vertical{height:100%}.f-thumbs__viewport{width:100%;height:auto;overflow:hidden;transform:translate3d(0, 0, 0)}.f-thumbs__track{display:flex}.f-thumbs__slide{position:relative;flex:0 0 auto;box-sizing:content-box;display:flex;align-items:center;justify-content:center;padding:0;margin:0;width:var(--f-thumb-width);height:var(--f-thumb-height);overflow:visible;cursor:pointer}.f-thumbs__slide.is-loading img{opacity:0}.is-classic .f-thumbs__viewport{height:100%}.is-modern .f-thumbs__track{width:max-content}.is-modern .f-thumbs__track::before{content:"";position:absolute;top:0;bottom:0;left:calc((var(--f-thumb-clip-width, 0))*-0.5);width:calc(var(--width, 0)*1px + var(--f-thumb-clip-width, 0));cursor:pointer}.is-modern .f-thumbs__slide{width:var(--f-thumb-clip-width);transform:translate3d(calc(var(--shift, 0) * -1px), 0, 0);transition:none;pointer-events:none}.is-modern.is-resting .f-thumbs__slide{transition:transform .33s ease}.is-modern.is-resting .f-thumbs__slide__button{transition:clip-path .33s ease}.is-using-tab .is-modern .f-thumbs__slide:focus-within{filter:drop-shadow(-1px 0px 0px var(--f-thumb-outline-color)) drop-shadow(2px 0px 0px var(--f-thumb-outline-color)) drop-shadow(0px -1px 0px var(--f-thumb-outline-color)) drop-shadow(0px 2px 0px var(--f-thumb-outline-color))}.f-thumbs__slide__button{appearance:none;width:var(--f-thumb-width);height:100%;margin:0 -100% 0 -100%;padding:0;border:0;position:relative;border-radius:var(--f-thumb-border-radius);overflow:hidden;background:rgba(0,0,0,0);outline:none;cursor:pointer;pointer-events:auto;touch-action:manipulation;opacity:var(--f-thumb-opacity);transition:opacity .2s ease}.f-thumbs__slide__button:hover{opacity:var(--f-thumb-hover-opacity)}.f-thumbs__slide__button:focus:not(:focus-visible){outline:none}.f-thumbs__slide__button:focus-visible{outline:none;opacity:var(--f-thumb-selected-opacity)}.is-modern .f-thumbs__slide__button{--clip-path: inset( 0 calc( ((var(--f-thumb-width, 0) - var(--f-thumb-clip-width, 0))) * (1 - var(--progress, 0)) * 0.5 ) round var(--f-thumb-border-radius, 0) );clip-path:var(--clip-path)}.is-classic .is-nav-selected .f-thumbs__slide__button{opacity:var(--f-thumb-selected-opacity)}.is-classic .is-nav-selected .f-thumbs__slide__button::after{content:"";position:absolute;top:0;left:0;right:0;height:auto;bottom:0;border:var(--f-thumb-outline, 0) solid var(--f-thumb-outline-color, transparent);border-radius:var(--f-thumb-border-radius);animation:f-fadeIn .2s ease-out;z-index:10}.f-thumbs__slide__img{overflow:hidden;position:absolute;top:0;right:0;bottom:0;left:0;width:100%;height:100%;margin:0;padding:var(--f-thumb-offset);box-sizing:border-box;pointer-events:none;object-fit:cover;border-radius:var(--f-thumb-border-radius)}.f-thumbs.is-horizontal .f-thumbs__track{padding:8px 0 12px 0}.f-thumbs.is-horizontal .f-thumbs__slide{margin:0 var(--f-thumb-gap) 0 0}.f-thumbs.is-vertical .f-thumbs__track{flex-wrap:wrap;padding:0 8px}.f-thumbs.is-vertical .f-thumbs__slide{margin:0 0 var(--f-thumb-gap) 0}.fancybox__thumbs{--f-thumb-width: 96px;--f-thumb-height: 72px;--f-thumb-border-radius: 2px;--f-thumb-outline: 2px;--f-thumb-outline-color: #ededed;position:relative;opacity:var(--fancybox-opacity, 1);transition:max-height .35s cubic-bezier(0.23, 1, 0.32, 1)}.fancybox__thumbs.is-classic{--f-thumb-gap: 8px;--f-thumb-opacity: 0.5;--f-thumb-hover-opacity: 1}.fancybox__thumbs.is-classic .f-spinner{background-image:linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05))}.fancybox__thumbs.is-modern{--f-thumb-gap: 4px;--f-thumb-extra-gap: 16px;--f-thumb-clip-width: 46px;--f-thumb-opacity: 1;--f-thumb-hover-opacity: 1}.fancybox__thumbs.is-modern .f-spinner{background-image:linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05))}.fancybox__thumbs.is-horizontal{padding:0 var(--f-thumb-gap)}.fancybox__thumbs.is-vertical{padding:var(--f-thumb-gap) 0}.is-compact .fancybox__thumbs{--f-thumb-width: 64px;--f-thumb-clip-width: 32px;--f-thumb-height: 48px;--f-thumb-extra-gap: 10px}.fancybox__thumbs.is-masked{max-height:0px !important}.is-closing .fancybox__thumbs{transition:none !important}.fancybox__toolbar{--f-progress-color: var(--fancybox-color, rgba(255, 255, 255, 0.94));--f-button-width: 46px;--f-button-height: 46px;--f-button-color: var(--fancybox-color);--f-button-hover-color: var(--fancybox-hover-color);--f-button-bg: rgba(24, 24, 27, 0.65);--f-button-hover-bg: rgba(70, 70, 73, 0.65);--f-button-active-bg: rgba(90, 90, 93, 0.65);--f-button-border-radius: 0;--f-button-svg-width: 24px;--f-button-svg-height: 24px;--f-button-svg-stroke-width: 1.5;--f-button-svg-filter: drop-shadow(1px 1px 1px rgba(24, 24, 27, 0.15));--f-button-svg-fill: none;--f-button-svg-disabled-opacity: 0.65;display:flex;flex-direction:row;justify-content:space-between;margin:0;padding:0;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI Adjusted","Segoe UI","Liberation Sans",sans-serif;color:var(--fancybox-color, currentColor);opacity:var(--fancybox-opacity, 1);text-shadow:var(--fancybox-toolbar-text-shadow, 1px 1px 1px rgba(0, 0, 0, 0.5));pointer-events:none;z-index:20}.fancybox__toolbar :focus-visible{z-index:1}.fancybox__toolbar.is-absolute,.is-compact .fancybox__toolbar{position:absolute;top:0;left:0;right:0}.is-idle .fancybox__toolbar{pointer-events:none;animation:.15s ease-out both f-fadeOut}.fancybox__toolbar__column{display:flex;flex-direction:row;flex-wrap:wrap;align-content:flex-start}.fancybox__toolbar__column.is-left,.fancybox__toolbar__column.is-right{flex-grow:1;flex-basis:0}.fancybox__toolbar__column.is-right{display:flex;justify-content:flex-end;flex-wrap:nowrap}.fancybox__infobar{padding:0 5px;line-height:var(--f-button-height);text-align:center;font-size:17px;font-variant-numeric:tabular-nums;-webkit-font-smoothing:subpixel-antialiased;cursor:default;user-select:none}.fancybox__infobar span{padding:0 5px}.fancybox__infobar:not(:first-child):not(:last-child){background:var(--f-button-bg)}[data-fancybox-toggle-slideshow]{position:relative}[data-fancybox-toggle-slideshow] .f-progress{height:100%;opacity:.3}[data-fancybox-toggle-slideshow] svg g:first-child{display:flex}[data-fancybox-toggle-slideshow] svg g:last-child{display:none}.has-slideshow [data-fancybox-toggle-slideshow] svg g:first-child{display:none}.has-slideshow [data-fancybox-toggle-slideshow] svg g:last-child{display:flex}[data-fancybox-toggle-fullscreen] svg g:first-child{display:flex}[data-fancybox-toggle-fullscreen] svg g:last-child{display:none}:fullscreen [data-fancybox-toggle-fullscreen] svg g:first-child{display:none}:fullscreen [data-fancybox-toggle-fullscreen] svg g:last-child{display:flex}.f-progress{position:absolute;top:0;left:0;right:0;height:3px;transform:scaleX(0);transform-origin:0;transition-property:transform;transition-timing-function:linear;background:var(--f-progress-color, var(--f-carousel-theme-color, #0091ff));z-index:30;user-select:none;pointer-events:none}',""]),e.Z=r},645:function(t){"use strict";t.exports=function(t){var e=[];return e.toString=function(){return this.map((function(e){var n="",i=void 0!==e[5];return e[4]&&(n+="@supports (".concat(e[4],") {")),e[2]&&(n+="@media ".concat(e[2]," {")),i&&(n+="@layer".concat(e[5].length>0?" ".concat(e[5]):""," {")),n+=t(e),i&&(n+="}"),e[2]&&(n+="}"),e[4]&&(n+="}"),n})).join("")},e.i=function(t,n,i,o,s){"string"==typeof t&&(t=[[null,t,void 0]]);var r={};if(i)for(var a=0;a<this.length;a++){var l=this[a][0];null!=l&&(r[l]=!0)}for(var c=0;c<t.length;c++){var u=[].concat(t[c]);i&&r[u[0]]||(void 0!==s&&(void 0===u[5]||(u[1]="@layer".concat(u[5].length>0?" ".concat(u[5]):""," {").concat(u[1],"}")),u[5]=s),n&&(u[2]?(u[1]="@media ".concat(u[2]," {").concat(u[1],"}"),u[2]=n):u[2]=n),o&&(u[4]?(u[1]="@supports (".concat(u[4],") {").concat(u[1],"}"),u[4]=o):u[4]="".concat(o)),e.push(u))}},e}},81:function(t){"use strict";t.exports=function(t){return t[1]}},755:function(t,e){var n;!function(e,n){"use strict";"object"==typeof t.exports?t.exports=e.document?n(e,!0):function(t){if(!t.document)throw new Error("jQuery requires a window with a document");return n(t)}:n(e)}("undefined"!=typeof window?window:this,(function(i,o){"use strict";var s=[],r=Object.getPrototypeOf,a=s.slice,l=s.flat?function(t){return s.flat.call(t)}:function(t){return s.concat.apply([],t)},c=s.push,u=s.indexOf,d={},h=d.toString,f=d.hasOwnProperty,p=f.toString,g=p.call(Object),m={},v=function(t){return"function"==typeof t&&"number"!=typeof t.nodeType&&"function"!=typeof t.item},b=function(t){return null!=t&&t===t.window},y=i.document,x={type:!0,src:!0,nonce:!0,noModule:!0};function w(t,e,n){var i,o,s=(n=n||y).createElement("script");if(s.text=t,e)for(i in x)(o=e[i]||e.getAttribute&&e.getAttribute(i))&&s.setAttribute(i,o);n.head.appendChild(s).parentNode.removeChild(s)}function E(t){return null==t?t+"":"object"==typeof t||"function"==typeof t?d[h.call(t)]||"object":typeof t}var S="3.7.1",T=/HTML$/i,C=function(t,e){return new C.fn.init(t,e)};function _(t){var e=!!t&&"length"in t&&t.length,n=E(t);return!v(t)&&!b(t)&&("array"===n||0===e||"number"==typeof e&&e>0&&e-1 in t)}function P(t,e){return t.nodeName&&t.nodeName.toLowerCase()===e.toLowerCase()}C.fn=C.prototype={jquery:S,constructor:C,length:0,toArray:function(){return a.call(this)},get:function(t){return null==t?a.call(this):t<0?this[t+this.length]:this[t]},pushStack:function(t){var e=C.merge(this.constructor(),t);return e.prevObject=this,e},each:function(t){return C.each(this,t)},map:function(t){return this.pushStack(C.map(this,(function(e,n){return t.call(e,n,e)})))},slice:function(){return this.pushStack(a.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(C.grep(this,(function(t,e){return(e+1)%2})))},odd:function(){return this.pushStack(C.grep(this,(function(t,e){return e%2})))},eq:function(t){var e=this.length,n=+t+(t<0?e:0);return this.pushStack(n>=0&&n<e?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:c,sort:s.sort,splice:s.splice},C.extend=C.fn.extend=function(){var t,e,n,i,o,s,r=arguments[0]||{},a=1,l=arguments.length,c=!1;for("boolean"==typeof r&&(c=r,r=arguments[a]||{},a++),"object"==typeof r||v(r)||(r={}),a===l&&(r=this,a--);a<l;a++)if(null!=(t=arguments[a]))for(e in t)i=t[e],"__proto__"!==e&&r!==i&&(c&&i&&(C.isPlainObject(i)||(o=Array.isArray(i)))?(n=r[e],s=o&&!Array.isArray(n)?[]:o||C.isPlainObject(n)?n:{},o=!1,r[e]=C.extend(c,s,i)):void 0!==i&&(r[e]=i));return r},C.extend({expando:"jQuery"+(S+Math.random()).replace(/\D/g,""),isReady:!0,error:function(t){throw new Error(t)},noop:function(){},isPlainObject:function(t){var e,n;return!(!t||"[object Object]"!==h.call(t)||(e=r(t))&&("function"!=typeof(n=f.call(e,"constructor")&&e.constructor)||p.call(n)!==g))},isEmptyObject:function(t){var e;for(e in t)return!1;return!0},globalEval:function(t,e,n){w(t,{nonce:e&&e.nonce},n)},each:function(t,e){var n,i=0;if(_(t))for(n=t.length;i<n&&!1!==e.call(t[i],i,t[i]);i++);else for(i in t)if(!1===e.call(t[i],i,t[i]))break;return t},text:function(t){var e,n="",i=0,o=t.nodeType;if(!o)for(;e=t[i++];)n+=C.text(e);return 1===o||11===o?t.textContent:9===o?t.documentElement.textContent:3===o||4===o?t.nodeValue:n},makeArray:function(t,e){var n=e||[];return null!=t&&(_(Object(t))?C.merge(n,"string"==typeof t?[t]:t):c.call(n,t)),n},inArray:function(t,e,n){return null==e?-1:u.call(e,t,n)},isXMLDoc:function(t){var e=t&&t.namespaceURI,n=t&&(t.ownerDocument||t).documentElement;return!T.test(e||n&&n.nodeName||"HTML")},merge:function(t,e){for(var n=+e.length,i=0,o=t.length;i<n;i++)t[o++]=e[i];return t.length=o,t},grep:function(t,e,n){for(var i=[],o=0,s=t.length,r=!n;o<s;o++)!e(t[o],o)!==r&&i.push(t[o]);return i},map:function(t,e,n){var i,o,s=0,r=[];if(_(t))for(i=t.length;s<i;s++)null!=(o=e(t[s],s,n))&&r.push(o);else for(s in t)null!=(o=e(t[s],s,n))&&r.push(o);return l(r)},guid:1,support:m}),"function"==typeof Symbol&&(C.fn[Symbol.iterator]=s[Symbol.iterator]),C.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),(function(t,e){d["[object "+e+"]"]=e.toLowerCase()}));var k=s.pop,O=s.sort,M=s.splice,A="[\\x20\\t\\r\\n\\f]",L=new RegExp("^"+A+"+|((?:^|[^\\\\])(?:\\\\.)*)"+A+"+$","g");C.contains=function(t,e){var n=e&&e.parentNode;return t===n||!(!n||1!==n.nodeType||!(t.contains?t.contains(n):t.compareDocumentPosition&&16&t.compareDocumentPosition(n)))};var z=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g;function D(t,e){return e?"\0"===t?"�":t.slice(0,-1)+"\\"+t.charCodeAt(t.length-1).toString(16)+" ":"\\"+t}C.escapeSelector=function(t){return(t+"").replace(z,D)};var j=y,I=c;!function(){var t,e,n,o,r,l,c,d,h,p,g=I,v=C.expando,b=0,y=0,x=tt(),w=tt(),E=tt(),S=tt(),T=function(t,e){return t===e&&(r=!0),0},_="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",z="(?:\\\\[\\da-fA-F]{1,6}"+A+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",D="\\["+A+"*("+z+")(?:"+A+"*([*^$|!~]?=)"+A+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+z+"))|)"+A+"*\\]",R=":("+z+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+D+")*)|.*)\\)|)",F=new RegExp(A+"+","g"),N=new RegExp("^"+A+"*,"+A+"*"),H=new RegExp("^"+A+"*([>+~]|"+A+")"+A+"*"),B=new RegExp(A+"|>"),q=new RegExp(R),$=new RegExp("^"+z+"$"),W={ID:new RegExp("^#("+z+")"),CLASS:new RegExp("^\\.("+z+")"),TAG:new RegExp("^("+z+"|[*])"),ATTR:new RegExp("^"+D),PSEUDO:new RegExp("^"+R),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+A+"*(even|odd|(([+-]|)(\\d*)n|)"+A+"*(?:([+-]|)"+A+"*(\\d+)|))"+A+"*\\)|)","i"),bool:new RegExp("^(?:"+_+")$","i"),needsContext:new RegExp("^"+A+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+A+"*((?:-\\d)?\\d*)"+A+"*\\)|)(?=[^-]|$)","i")},X=/^(?:input|select|textarea|button)$/i,Y=/^h\d$/i,U=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,V=/[+~]/,Z=new RegExp("\\\\[\\da-fA-F]{1,6}"+A+"?|\\\\([^\\r\\n\\f])","g"),G=function(t,e){var n="0x"+t.slice(1)-65536;return e||(n<0?String.fromCharCode(n+65536):String.fromCharCode(n>>10|55296,1023&n|56320))},K=function(){lt()},J=ht((function(t){return!0===t.disabled&&P(t,"fieldset")}),{dir:"parentNode",next:"legend"});try{g.apply(s=a.call(j.childNodes),j.childNodes),s[j.childNodes.length].nodeType}catch(t){g={apply:function(t,e){I.apply(t,a.call(e))},call:function(t){I.apply(t,a.call(arguments,1))}}}function Q(t,e,n,i){var o,s,r,a,c,u,f,p=e&&e.ownerDocument,b=e?e.nodeType:9;if(n=n||[],"string"!=typeof t||!t||1!==b&&9!==b&&11!==b)return n;if(!i&&(lt(e),e=e||l,d)){if(11!==b&&(c=U.exec(t)))if(o=c[1]){if(9===b){if(!(r=e.getElementById(o)))return n;if(r.id===o)return g.call(n,r),n}else if(p&&(r=p.getElementById(o))&&Q.contains(e,r)&&r.id===o)return g.call(n,r),n}else{if(c[2])return g.apply(n,e.getElementsByTagName(t)),n;if((o=c[3])&&e.getElementsByClassName)return g.apply(n,e.getElementsByClassName(o)),n}if(!(S[t+" "]||h&&h.test(t))){if(f=t,p=e,1===b&&(B.test(t)||H.test(t))){for((p=V.test(t)&&at(e.parentNode)||e)==e&&m.scope||((a=e.getAttribute("id"))?a=C.escapeSelector(a):e.setAttribute("id",a=v)),s=(u=ut(t)).length;s--;)u[s]=(a?"#"+a:":scope")+" "+dt(u[s]);f=u.join(",")}try{return g.apply(n,p.querySelectorAll(f)),n}catch(e){S(t,!0)}finally{a===v&&e.removeAttribute("id")}}}return bt(t.replace(L,"$1"),e,n,i)}function tt(){var t=[];return function n(i,o){return t.push(i+" ")>e.cacheLength&&delete n[t.shift()],n[i+" "]=o}}function et(t){return t[v]=!0,t}function nt(t){var e=l.createElement("fieldset");try{return!!t(e)}catch(t){return!1}finally{e.parentNode&&e.parentNode.removeChild(e),e=null}}function it(t){return function(e){return P(e,"input")&&e.type===t}}function ot(t){return function(e){return(P(e,"input")||P(e,"button"))&&e.type===t}}function st(t){return function(e){return"form"in e?e.parentNode&&!1===e.disabled?"label"in e?"label"in e.parentNode?e.parentNode.disabled===t:e.disabled===t:e.isDisabled===t||e.isDisabled!==!t&&J(e)===t:e.disabled===t:"label"in e&&e.disabled===t}}function rt(t){return et((function(e){return e=+e,et((function(n,i){for(var o,s=t([],n.length,e),r=s.length;r--;)n[o=s[r]]&&(n[o]=!(i[o]=n[o]))}))}))}function at(t){return t&&void 0!==t.getElementsByTagName&&t}function lt(t){var n,i=t?t.ownerDocument||t:j;return i!=l&&9===i.nodeType&&i.documentElement?(c=(l=i).documentElement,d=!C.isXMLDoc(l),p=c.matches||c.webkitMatchesSelector||c.msMatchesSelector,c.msMatchesSelector&&j!=l&&(n=l.defaultView)&&n.top!==n&&n.addEventListener("unload",K),m.getById=nt((function(t){return c.appendChild(t).id=C.expando,!l.getElementsByName||!l.getElementsByName(C.expando).length})),m.disconnectedMatch=nt((function(t){return p.call(t,"*")})),m.scope=nt((function(){return l.querySelectorAll(":scope")})),m.cssHas=nt((function(){try{return l.querySelector(":has(*,:jqfake)"),!1}catch(t){return!0}})),m.getById?(e.filter.ID=function(t){var e=t.replace(Z,G);return function(t){return t.getAttribute("id")===e}},e.find.ID=function(t,e){if(void 0!==e.getElementById&&d){var n=e.getElementById(t);return n?[n]:[]}}):(e.filter.ID=function(t){var e=t.replace(Z,G);return function(t){var n=void 0!==t.getAttributeNode&&t.getAttributeNode("id");return n&&n.value===e}},e.find.ID=function(t,e){if(void 0!==e.getElementById&&d){var n,i,o,s=e.getElementById(t);if(s){if((n=s.getAttributeNode("id"))&&n.value===t)return[s];for(o=e.getElementsByName(t),i=0;s=o[i++];)if((n=s.getAttributeNode("id"))&&n.value===t)return[s]}return[]}}),e.find.TAG=function(t,e){return void 0!==e.getElementsByTagName?e.getElementsByTagName(t):e.querySelectorAll(t)},e.find.CLASS=function(t,e){if(void 0!==e.getElementsByClassName&&d)return e.getElementsByClassName(t)},h=[],nt((function(t){var e;c.appendChild(t).innerHTML="<a id='"+v+"' href='' disabled='disabled'></a><select id='"+v+"-\r\\' disabled='disabled'><option selected=''></option></select>",t.querySelectorAll("[selected]").length||h.push("\\["+A+"*(?:value|"+_+")"),t.querySelectorAll("[id~="+v+"-]").length||h.push("~="),t.querySelectorAll("a#"+v+"+*").length||h.push(".#.+[+~]"),t.querySelectorAll(":checked").length||h.push(":checked"),(e=l.createElement("input")).setAttribute("type","hidden"),t.appendChild(e).setAttribute("name","D"),c.appendChild(t).disabled=!0,2!==t.querySelectorAll(":disabled").length&&h.push(":enabled",":disabled"),(e=l.createElement("input")).setAttribute("name",""),t.appendChild(e),t.querySelectorAll("[name='']").length||h.push("\\["+A+"*name"+A+"*="+A+"*(?:''|\"\")")})),m.cssHas||h.push(":has"),h=h.length&&new RegExp(h.join("|")),T=function(t,e){if(t===e)return r=!0,0;var n=!t.compareDocumentPosition-!e.compareDocumentPosition;return n||(1&(n=(t.ownerDocument||t)==(e.ownerDocument||e)?t.compareDocumentPosition(e):1)||!m.sortDetached&&e.compareDocumentPosition(t)===n?t===l||t.ownerDocument==j&&Q.contains(j,t)?-1:e===l||e.ownerDocument==j&&Q.contains(j,e)?1:o?u.call(o,t)-u.call(o,e):0:4&n?-1:1)},l):l}for(t in Q.matches=function(t,e){return Q(t,null,null,e)},Q.matchesSelector=function(t,e){if(lt(t),d&&!S[e+" "]&&(!h||!h.test(e)))try{var n=p.call(t,e);if(n||m.disconnectedMatch||t.document&&11!==t.document.nodeType)return n}catch(t){S(e,!0)}return Q(e,l,null,[t]).length>0},Q.contains=function(t,e){return(t.ownerDocument||t)!=l&&lt(t),C.contains(t,e)},Q.attr=function(t,n){(t.ownerDocument||t)!=l&&lt(t);var i=e.attrHandle[n.toLowerCase()],o=i&&f.call(e.attrHandle,n.toLowerCase())?i(t,n,!d):void 0;return void 0!==o?o:t.getAttribute(n)},Q.error=function(t){throw new Error("Syntax error, unrecognized expression: "+t)},C.uniqueSort=function(t){var e,n=[],i=0,s=0;if(r=!m.sortStable,o=!m.sortStable&&a.call(t,0),O.call(t,T),r){for(;e=t[s++];)e===t[s]&&(i=n.push(s));for(;i--;)M.call(t,n[i],1)}return o=null,t},C.fn.uniqueSort=function(){return this.pushStack(C.uniqueSort(a.apply(this)))},e=C.expr={cacheLength:50,createPseudo:et,match:W,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(t){return t[1]=t[1].replace(Z,G),t[3]=(t[3]||t[4]||t[5]||"").replace(Z,G),"~="===t[2]&&(t[3]=" "+t[3]+" "),t.slice(0,4)},CHILD:function(t){return t[1]=t[1].toLowerCase(),"nth"===t[1].slice(0,3)?(t[3]||Q.error(t[0]),t[4]=+(t[4]?t[5]+(t[6]||1):2*("even"===t[3]||"odd"===t[3])),t[5]=+(t[7]+t[8]||"odd"===t[3])):t[3]&&Q.error(t[0]),t},PSEUDO:function(t){var e,n=!t[6]&&t[2];return W.CHILD.test(t[0])?null:(t[3]?t[2]=t[4]||t[5]||"":n&&q.test(n)&&(e=ut(n,!0))&&(e=n.indexOf(")",n.length-e)-n.length)&&(t[0]=t[0].slice(0,e),t[2]=n.slice(0,e)),t.slice(0,3))}},filter:{TAG:function(t){var e=t.replace(Z,G).toLowerCase();return"*"===t?function(){return!0}:function(t){return P(t,e)}},CLASS:function(t){var e=x[t+" "];return e||(e=new RegExp("(^|"+A+")"+t+"("+A+"|$)"))&&x(t,(function(t){return e.test("string"==typeof t.className&&t.className||void 0!==t.getAttribute&&t.getAttribute("class")||"")}))},ATTR:function(t,e,n){return function(i){var o=Q.attr(i,t);return null==o?"!="===e:!e||(o+="","="===e?o===n:"!="===e?o!==n:"^="===e?n&&0===o.indexOf(n):"*="===e?n&&o.indexOf(n)>-1:"$="===e?n&&o.slice(-n.length)===n:"~="===e?(" "+o.replace(F," ")+" ").indexOf(n)>-1:"|="===e&&(o===n||o.slice(0,n.length+1)===n+"-"))}},CHILD:function(t,e,n,i,o){var s="nth"!==t.slice(0,3),r="last"!==t.slice(-4),a="of-type"===e;return 1===i&&0===o?function(t){return!!t.parentNode}:function(e,n,l){var c,u,d,h,f,p=s!==r?"nextSibling":"previousSibling",g=e.parentNode,m=a&&e.nodeName.toLowerCase(),y=!l&&!a,x=!1;if(g){if(s){for(;p;){for(d=e;d=d[p];)if(a?P(d,m):1===d.nodeType)return!1;f=p="only"===t&&!f&&"nextSibling"}return!0}if(f=[r?g.firstChild:g.lastChild],r&&y){for(x=(h=(c=(u=g[v]||(g[v]={}))[t]||[])[0]===b&&c[1])&&c[2],d=h&&g.childNodes[h];d=++h&&d&&d[p]||(x=h=0)||f.pop();)if(1===d.nodeType&&++x&&d===e){u[t]=[b,h,x];break}}else if(y&&(x=h=(c=(u=e[v]||(e[v]={}))[t]||[])[0]===b&&c[1]),!1===x)for(;(d=++h&&d&&d[p]||(x=h=0)||f.pop())&&(!(a?P(d,m):1===d.nodeType)||!++x||(y&&((u=d[v]||(d[v]={}))[t]=[b,x]),d!==e)););return(x-=o)===i||x%i==0&&x/i>=0}}},PSEUDO:function(t,n){var i,o=e.pseudos[t]||e.setFilters[t.toLowerCase()]||Q.error("unsupported pseudo: "+t);return o[v]?o(n):o.length>1?(i=[t,t,"",n],e.setFilters.hasOwnProperty(t.toLowerCase())?et((function(t,e){for(var i,s=o(t,n),r=s.length;r--;)t[i=u.call(t,s[r])]=!(e[i]=s[r])})):function(t){return o(t,0,i)}):o}},pseudos:{not:et((function(t){var e=[],n=[],i=vt(t.replace(L,"$1"));return i[v]?et((function(t,e,n,o){for(var s,r=i(t,null,o,[]),a=t.length;a--;)(s=r[a])&&(t[a]=!(e[a]=s))})):function(t,o,s){return e[0]=t,i(e,null,s,n),e[0]=null,!n.pop()}})),has:et((function(t){return function(e){return Q(t,e).length>0}})),contains:et((function(t){return t=t.replace(Z,G),function(e){return(e.textContent||C.text(e)).indexOf(t)>-1}})),lang:et((function(t){return $.test(t||"")||Q.error("unsupported lang: "+t),t=t.replace(Z,G).toLowerCase(),function(e){var n;do{if(n=d?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(n=n.toLowerCase())===t||0===n.indexOf(t+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}})),target:function(t){var e=i.location&&i.location.hash;return e&&e.slice(1)===t.id},root:function(t){return t===c},focus:function(t){return t===function(){try{return l.activeElement}catch(t){}}()&&l.hasFocus()&&!!(t.type||t.href||~t.tabIndex)},enabled:st(!1),disabled:st(!0),checked:function(t){return P(t,"input")&&!!t.checked||P(t,"option")&&!!t.selected},selected:function(t){return t.parentNode&&t.parentNode.selectedIndex,!0===t.selected},empty:function(t){for(t=t.firstChild;t;t=t.nextSibling)if(t.nodeType<6)return!1;return!0},parent:function(t){return!e.pseudos.empty(t)},header:function(t){return Y.test(t.nodeName)},input:function(t){return X.test(t.nodeName)},button:function(t){return P(t,"input")&&"button"===t.type||P(t,"button")},text:function(t){var e;return P(t,"input")&&"text"===t.type&&(null==(e=t.getAttribute("type"))||"text"===e.toLowerCase())},first:rt((function(){return[0]})),last:rt((function(t,e){return[e-1]})),eq:rt((function(t,e,n){return[n<0?n+e:n]})),even:rt((function(t,e){for(var n=0;n<e;n+=2)t.push(n);return t})),odd:rt((function(t,e){for(var n=1;n<e;n+=2)t.push(n);return t})),lt:rt((function(t,e,n){var i;for(i=n<0?n+e:n>e?e:n;--i>=0;)t.push(i);return t})),gt:rt((function(t,e,n){for(var i=n<0?n+e:n;++i<e;)t.push(i);return t}))}},e.pseudos.nth=e.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})e.pseudos[t]=it(t);for(t in{submit:!0,reset:!0})e.pseudos[t]=ot(t);function ct(){}function ut(t,n){var i,o,s,r,a,l,c,u=w[t+" "];if(u)return n?0:u.slice(0);for(a=t,l=[],c=e.preFilter;a;){for(r in i&&!(o=N.exec(a))||(o&&(a=a.slice(o[0].length)||a),l.push(s=[])),i=!1,(o=H.exec(a))&&(i=o.shift(),s.push({value:i,type:o[0].replace(L," ")}),a=a.slice(i.length)),e.filter)!(o=W[r].exec(a))||c[r]&&!(o=c[r](o))||(i=o.shift(),s.push({value:i,type:r,matches:o}),a=a.slice(i.length));if(!i)break}return n?a.length:a?Q.error(t):w(t,l).slice(0)}function dt(t){for(var e=0,n=t.length,i="";e<n;e++)i+=t[e].value;return i}function ht(t,e,n){var i=e.dir,o=e.next,s=o||i,r=n&&"parentNode"===s,a=y++;return e.first?function(e,n,o){for(;e=e[i];)if(1===e.nodeType||r)return t(e,n,o);return!1}:function(e,n,l){var c,u,d=[b,a];if(l){for(;e=e[i];)if((1===e.nodeType||r)&&t(e,n,l))return!0}else for(;e=e[i];)if(1===e.nodeType||r)if(u=e[v]||(e[v]={}),o&&P(e,o))e=e[i]||e;else{if((c=u[s])&&c[0]===b&&c[1]===a)return d[2]=c[2];if(u[s]=d,d[2]=t(e,n,l))return!0}return!1}}function ft(t){return t.length>1?function(e,n,i){for(var o=t.length;o--;)if(!t[o](e,n,i))return!1;return!0}:t[0]}function pt(t,e,n,i,o){for(var s,r=[],a=0,l=t.length,c=null!=e;a<l;a++)(s=t[a])&&(n&&!n(s,i,o)||(r.push(s),c&&e.push(a)));return r}function gt(t,e,n,i,o,s){return i&&!i[v]&&(i=gt(i)),o&&!o[v]&&(o=gt(o,s)),et((function(s,r,a,l){var c,d,h,f,p=[],m=[],v=r.length,b=s||function(t,e,n){for(var i=0,o=e.length;i<o;i++)Q(t,e[i],n);return n}(e||"*",a.nodeType?[a]:a,[]),y=!t||!s&&e?b:pt(b,p,t,a,l);if(n?n(y,f=o||(s?t:v||i)?[]:r,a,l):f=y,i)for(c=pt(f,m),i(c,[],a,l),d=c.length;d--;)(h=c[d])&&(f[m[d]]=!(y[m[d]]=h));if(s){if(o||t){if(o){for(c=[],d=f.length;d--;)(h=f[d])&&c.push(y[d]=h);o(null,f=[],c,l)}for(d=f.length;d--;)(h=f[d])&&(c=o?u.call(s,h):p[d])>-1&&(s[c]=!(r[c]=h))}}else f=pt(f===r?f.splice(v,f.length):f),o?o(null,r,f,l):g.apply(r,f)}))}function mt(t){for(var i,o,s,r=t.length,a=e.relative[t[0].type],l=a||e.relative[" "],c=a?1:0,d=ht((function(t){return t===i}),l,!0),h=ht((function(t){return u.call(i,t)>-1}),l,!0),f=[function(t,e,o){var s=!a&&(o||e!=n)||((i=e).nodeType?d(t,e,o):h(t,e,o));return i=null,s}];c<r;c++)if(o=e.relative[t[c].type])f=[ht(ft(f),o)];else{if((o=e.filter[t[c].type].apply(null,t[c].matches))[v]){for(s=++c;s<r&&!e.relative[t[s].type];s++);return gt(c>1&&ft(f),c>1&&dt(t.slice(0,c-1).concat({value:" "===t[c-2].type?"*":""})).replace(L,"$1"),o,c<s&&mt(t.slice(c,s)),s<r&&mt(t=t.slice(s)),s<r&&dt(t))}f.push(o)}return ft(f)}function vt(t,i){var o,s=[],r=[],a=E[t+" "];if(!a){for(i||(i=ut(t)),o=i.length;o--;)(a=mt(i[o]))[v]?s.push(a):r.push(a);a=E(t,function(t,i){var o=i.length>0,s=t.length>0,r=function(r,a,c,u,h){var f,p,m,v=0,y="0",x=r&&[],w=[],E=n,S=r||s&&e.find.TAG("*",h),T=b+=null==E?1:Math.random()||.1,_=S.length;for(h&&(n=a==l||a||h);y!==_&&null!=(f=S[y]);y++){if(s&&f){for(p=0,a||f.ownerDocument==l||(lt(f),c=!d);m=t[p++];)if(m(f,a||l,c)){g.call(u,f);break}h&&(b=T)}o&&((f=!m&&f)&&v--,r&&x.push(f))}if(v+=y,o&&y!==v){for(p=0;m=i[p++];)m(x,w,a,c);if(r){if(v>0)for(;y--;)x[y]||w[y]||(w[y]=k.call(u));w=pt(w)}g.apply(u,w),h&&!r&&w.length>0&&v+i.length>1&&C.uniqueSort(u)}return h&&(b=T,n=E),x};return o?et(r):r}(r,s)),a.selector=t}return a}function bt(t,n,i,o){var s,r,a,l,c,u="function"==typeof t&&t,h=!o&&ut(t=u.selector||t);if(i=i||[],1===h.length){if((r=h[0]=h[0].slice(0)).length>2&&"ID"===(a=r[0]).type&&9===n.nodeType&&d&&e.relative[r[1].type]){if(!(n=(e.find.ID(a.matches[0].replace(Z,G),n)||[])[0]))return i;u&&(n=n.parentNode),t=t.slice(r.shift().value.length)}for(s=W.needsContext.test(t)?0:r.length;s--&&(a=r[s],!e.relative[l=a.type]);)if((c=e.find[l])&&(o=c(a.matches[0].replace(Z,G),V.test(r[0].type)&&at(n.parentNode)||n))){if(r.splice(s,1),!(t=o.length&&dt(r)))return g.apply(i,o),i;break}}return(u||vt(t,h))(o,n,!d,i,!n||V.test(t)&&at(n.parentNode)||n),i}ct.prototype=e.filters=e.pseudos,e.setFilters=new ct,m.sortStable=v.split("").sort(T).join("")===v,lt(),m.sortDetached=nt((function(t){return 1&t.compareDocumentPosition(l.createElement("fieldset"))})),C.find=Q,C.expr[":"]=C.expr.pseudos,C.unique=C.uniqueSort,Q.compile=vt,Q.select=bt,Q.setDocument=lt,Q.tokenize=ut,Q.escape=C.escapeSelector,Q.getText=C.text,Q.isXML=C.isXMLDoc,Q.selectors=C.expr,Q.support=C.support,Q.uniqueSort=C.uniqueSort}();var R=function(t,e,n){for(var i=[],o=void 0!==n;(t=t[e])&&9!==t.nodeType;)if(1===t.nodeType){if(o&&C(t).is(n))break;i.push(t)}return i},F=function(t,e){for(var n=[];t;t=t.nextSibling)1===t.nodeType&&t!==e&&n.push(t);return n},N=C.expr.match.needsContext,H=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function B(t,e,n){return v(e)?C.grep(t,(function(t,i){return!!e.call(t,i,t)!==n})):e.nodeType?C.grep(t,(function(t){return t===e!==n})):"string"!=typeof e?C.grep(t,(function(t){return u.call(e,t)>-1!==n})):C.filter(e,t,n)}C.filter=function(t,e,n){var i=e[0];return n&&(t=":not("+t+")"),1===e.length&&1===i.nodeType?C.find.matchesSelector(i,t)?[i]:[]:C.find.matches(t,C.grep(e,(function(t){return 1===t.nodeType})))},C.fn.extend({find:function(t){var e,n,i=this.length,o=this;if("string"!=typeof t)return this.pushStack(C(t).filter((function(){for(e=0;e<i;e++)if(C.contains(o[e],this))return!0})));for(n=this.pushStack([]),e=0;e<i;e++)C.find(t,o[e],n);return i>1?C.uniqueSort(n):n},filter:function(t){return this.pushStack(B(this,t||[],!1))},not:function(t){return this.pushStack(B(this,t||[],!0))},is:function(t){return!!B(this,"string"==typeof t&&N.test(t)?C(t):t||[],!1).length}});var q,$=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(C.fn.init=function(t,e,n){var i,o;if(!t)return this;if(n=n||q,"string"==typeof t){if(!(i="<"===t[0]&&">"===t[t.length-1]&&t.length>=3?[null,t,null]:$.exec(t))||!i[1]&&e)return!e||e.jquery?(e||n).find(t):this.constructor(e).find(t);if(i[1]){if(e=e instanceof C?e[0]:e,C.merge(this,C.parseHTML(i[1],e&&e.nodeType?e.ownerDocument||e:y,!0)),H.test(i[1])&&C.isPlainObject(e))for(i in e)v(this[i])?this[i](e[i]):this.attr(i,e[i]);return this}return(o=y.getElementById(i[2]))&&(this[0]=o,this.length=1),this}return t.nodeType?(this[0]=t,this.length=1,this):v(t)?void 0!==n.ready?n.ready(t):t(C):C.makeArray(t,this)}).prototype=C.fn,q=C(y);var W=/^(?:parents|prev(?:Until|All))/,X={children:!0,contents:!0,next:!0,prev:!0};function Y(t,e){for(;(t=t[e])&&1!==t.nodeType;);return t}C.fn.extend({has:function(t){var e=C(t,this),n=e.length;return this.filter((function(){for(var t=0;t<n;t++)if(C.contains(this,e[t]))return!0}))},closest:function(t,e){var n,i=0,o=this.length,s=[],r="string"!=typeof t&&C(t);if(!N.test(t))for(;i<o;i++)for(n=this[i];n&&n!==e;n=n.parentNode)if(n.nodeType<11&&(r?r.index(n)>-1:1===n.nodeType&&C.find.matchesSelector(n,t))){s.push(n);break}return this.pushStack(s.length>1?C.uniqueSort(s):s)},index:function(t){return t?"string"==typeof t?u.call(C(t),this[0]):u.call(this,t.jquery?t[0]:t):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(t,e){return this.pushStack(C.uniqueSort(C.merge(this.get(),C(t,e))))},addBack:function(t){return this.add(null==t?this.prevObject:this.prevObject.filter(t))}}),C.each({parent:function(t){var e=t.parentNode;return e&&11!==e.nodeType?e:null},parents:function(t){return R(t,"parentNode")},parentsUntil:function(t,e,n){return R(t,"parentNode",n)},next:function(t){return Y(t,"nextSibling")},prev:function(t){return Y(t,"previousSibling")},nextAll:function(t){return R(t,"nextSibling")},prevAll:function(t){return R(t,"previousSibling")},nextUntil:function(t,e,n){return R(t,"nextSibling",n)},prevUntil:function(t,e,n){return R(t,"previousSibling",n)},siblings:function(t){return F((t.parentNode||{}).firstChild,t)},children:function(t){return F(t.firstChild)},contents:function(t){return null!=t.contentDocument&&r(t.contentDocument)?t.contentDocument:(P(t,"template")&&(t=t.content||t),C.merge([],t.childNodes))}},(function(t,e){C.fn[t]=function(n,i){var o=C.map(this,e,n);return"Until"!==t.slice(-5)&&(i=n),i&&"string"==typeof i&&(o=C.filter(i,o)),this.length>1&&(X[t]||C.uniqueSort(o),W.test(t)&&o.reverse()),this.pushStack(o)}}));var U=/[^\x20\t\r\n\f]+/g;function V(t){return t}function Z(t){throw t}function G(t,e,n,i){var o;try{t&&v(o=t.promise)?o.call(t).done(e).fail(n):t&&v(o=t.then)?o.call(t,e,n):e.apply(void 0,[t].slice(i))}catch(t){n.apply(void 0,[t])}}C.Callbacks=function(t){t="string"==typeof t?function(t){var e={};return C.each(t.match(U)||[],(function(t,n){e[n]=!0})),e}(t):C.extend({},t);var e,n,i,o,s=[],r=[],a=-1,l=function(){for(o=o||t.once,i=e=!0;r.length;a=-1)for(n=r.shift();++a<s.length;)!1===s[a].apply(n[0],n[1])&&t.stopOnFalse&&(a=s.length,n=!1);t.memory||(n=!1),e=!1,o&&(s=n?[]:"")},c={add:function(){return s&&(n&&!e&&(a=s.length-1,r.push(n)),function e(n){C.each(n,(function(n,i){v(i)?t.unique&&c.has(i)||s.push(i):i&&i.length&&"string"!==E(i)&&e(i)}))}(arguments),n&&!e&&l()),this},remove:function(){return C.each(arguments,(function(t,e){for(var n;(n=C.inArray(e,s,n))>-1;)s.splice(n,1),n<=a&&a--})),this},has:function(t){return t?C.inArray(t,s)>-1:s.length>0},empty:function(){return s&&(s=[]),this},disable:function(){return o=r=[],s=n="",this},disabled:function(){return!s},lock:function(){return o=r=[],n||e||(s=n=""),this},locked:function(){return!!o},fireWith:function(t,n){return o||(n=[t,(n=n||[]).slice?n.slice():n],r.push(n),e||l()),this},fire:function(){return c.fireWith(this,arguments),this},fired:function(){return!!i}};return c},C.extend({Deferred:function(t){var e=[["notify","progress",C.Callbacks("memory"),C.Callbacks("memory"),2],["resolve","done",C.Callbacks("once memory"),C.Callbacks("once memory"),0,"resolved"],["reject","fail",C.Callbacks("once memory"),C.Callbacks("once memory"),1,"rejected"]],n="pending",o={state:function(){return n},always:function(){return s.done(arguments).fail(arguments),this},catch:function(t){return o.then(null,t)},pipe:function(){var t=arguments;return C.Deferred((function(n){C.each(e,(function(e,i){var o=v(t[i[4]])&&t[i[4]];s[i[1]]((function(){var t=o&&o.apply(this,arguments);t&&v(t.promise)?t.promise().progress(n.notify).done(n.resolve).fail(n.reject):n[i[0]+"With"](this,o?[t]:arguments)}))})),t=null})).promise()},then:function(t,n,o){var s=0;function r(t,e,n,o){return function(){var a=this,l=arguments,c=function(){var i,c;if(!(t<s)){if((i=n.apply(a,l))===e.promise())throw new TypeError("Thenable self-resolution");c=i&&("object"==typeof i||"function"==typeof i)&&i.then,v(c)?o?c.call(i,r(s,e,V,o),r(s,e,Z,o)):(s++,c.call(i,r(s,e,V,o),r(s,e,Z,o),r(s,e,V,e.notifyWith))):(n!==V&&(a=void 0,l=[i]),(o||e.resolveWith)(a,l))}},u=o?c:function(){try{c()}catch(i){C.Deferred.exceptionHook&&C.Deferred.exceptionHook(i,u.error),t+1>=s&&(n!==Z&&(a=void 0,l=[i]),e.rejectWith(a,l))}};t?u():(C.Deferred.getErrorHook?u.error=C.Deferred.getErrorHook():C.Deferred.getStackHook&&(u.error=C.Deferred.getStackHook()),i.setTimeout(u))}}return C.Deferred((function(i){e[0][3].add(r(0,i,v(o)?o:V,i.notifyWith)),e[1][3].add(r(0,i,v(t)?t:V)),e[2][3].add(r(0,i,v(n)?n:Z))})).promise()},promise:function(t){return null!=t?C.extend(t,o):o}},s={};return C.each(e,(function(t,i){var r=i[2],a=i[5];o[i[1]]=r.add,a&&r.add((function(){n=a}),e[3-t][2].disable,e[3-t][3].disable,e[0][2].lock,e[0][3].lock),r.add(i[3].fire),s[i[0]]=function(){return s[i[0]+"With"](this===s?void 0:this,arguments),this},s[i[0]+"With"]=r.fireWith})),o.promise(s),t&&t.call(s,s),s},when:function(t){var e=arguments.length,n=e,i=Array(n),o=a.call(arguments),s=C.Deferred(),r=function(t){return function(n){i[t]=this,o[t]=arguments.length>1?a.call(arguments):n,--e||s.resolveWith(i,o)}};if(e<=1&&(G(t,s.done(r(n)).resolve,s.reject,!e),"pending"===s.state()||v(o[n]&&o[n].then)))return s.then();for(;n--;)G(o[n],r(n),s.reject);return s.promise()}});var K=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;C.Deferred.exceptionHook=function(t,e){i.console&&i.console.warn&&t&&K.test(t.name)&&i.console.warn("jQuery.Deferred exception: "+t.message,t.stack,e)},C.readyException=function(t){i.setTimeout((function(){throw t}))};var J=C.Deferred();function Q(){y.removeEventListener("DOMContentLoaded",Q),i.removeEventListener("load",Q),C.ready()}C.fn.ready=function(t){return J.then(t).catch((function(t){C.readyException(t)})),this},C.extend({isReady:!1,readyWait:1,ready:function(t){(!0===t?--C.readyWait:C.isReady)||(C.isReady=!0,!0!==t&&--C.readyWait>0||J.resolveWith(y,[C]))}}),C.ready.then=J.then,"complete"===y.readyState||"loading"!==y.readyState&&!y.documentElement.doScroll?i.setTimeout(C.ready):(y.addEventListener("DOMContentLoaded",Q),i.addEventListener("load",Q));var tt=function(t,e,n,i,o,s,r){var a=0,l=t.length,c=null==n;if("object"===E(n))for(a in o=!0,n)tt(t,e,a,n[a],!0,s,r);else if(void 0!==i&&(o=!0,v(i)||(r=!0),c&&(r?(e.call(t,i),e=null):(c=e,e=function(t,e,n){return c.call(C(t),n)})),e))for(;a<l;a++)e(t[a],n,r?i:i.call(t[a],a,e(t[a],n)));return o?t:c?e.call(t):l?e(t[0],n):s},et=/^-ms-/,nt=/-([a-z])/g;function it(t,e){return e.toUpperCase()}function ot(t){return t.replace(et,"ms-").replace(nt,it)}var st=function(t){return 1===t.nodeType||9===t.nodeType||!+t.nodeType};function rt(){this.expando=C.expando+rt.uid++}rt.uid=1,rt.prototype={cache:function(t){var e=t[this.expando];return e||(e={},st(t)&&(t.nodeType?t[this.expando]=e:Object.defineProperty(t,this.expando,{value:e,configurable:!0}))),e},set:function(t,e,n){var i,o=this.cache(t);if("string"==typeof e)o[ot(e)]=n;else for(i in e)o[ot(i)]=e[i];return o},get:function(t,e){return void 0===e?this.cache(t):t[this.expando]&&t[this.expando][ot(e)]},access:function(t,e,n){return void 0===e||e&&"string"==typeof e&&void 0===n?this.get(t,e):(this.set(t,e,n),void 0!==n?n:e)},remove:function(t,e){var n,i=t[this.expando];if(void 0!==i){if(void 0!==e){n=(e=Array.isArray(e)?e.map(ot):(e=ot(e))in i?[e]:e.match(U)||[]).length;for(;n--;)delete i[e[n]]}(void 0===e||C.isEmptyObject(i))&&(t.nodeType?t[this.expando]=void 0:delete t[this.expando])}},hasData:function(t){var e=t[this.expando];return void 0!==e&&!C.isEmptyObject(e)}};var at=new rt,lt=new rt,ct=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,ut=/[A-Z]/g;function dt(t,e,n){var i;if(void 0===n&&1===t.nodeType)if(i="data-"+e.replace(ut,"-$&").toLowerCase(),"string"==typeof(n=t.getAttribute(i))){try{n=function(t){return"true"===t||"false"!==t&&("null"===t?null:t===+t+""?+t:ct.test(t)?JSON.parse(t):t)}(n)}catch(t){}lt.set(t,e,n)}else n=void 0;return n}C.extend({hasData:function(t){return lt.hasData(t)||at.hasData(t)},data:function(t,e,n){return lt.access(t,e,n)},removeData:function(t,e){lt.remove(t,e)},_data:function(t,e,n){return at.access(t,e,n)},_removeData:function(t,e){at.remove(t,e)}}),C.fn.extend({data:function(t,e){var n,i,o,s=this[0],r=s&&s.attributes;if(void 0===t){if(this.length&&(o=lt.get(s),1===s.nodeType&&!at.get(s,"hasDataAttrs"))){for(n=r.length;n--;)r[n]&&0===(i=r[n].name).indexOf("data-")&&(i=ot(i.slice(5)),dt(s,i,o[i]));at.set(s,"hasDataAttrs",!0)}return o}return"object"==typeof t?this.each((function(){lt.set(this,t)})):tt(this,(function(e){var n;if(s&&void 0===e)return void 0!==(n=lt.get(s,t))||void 0!==(n=dt(s,t))?n:void 0;this.each((function(){lt.set(this,t,e)}))}),null,e,arguments.length>1,null,!0)},removeData:function(t){return this.each((function(){lt.remove(this,t)}))}}),C.extend({queue:function(t,e,n){var i;if(t)return e=(e||"fx")+"queue",i=at.get(t,e),n&&(!i||Array.isArray(n)?i=at.access(t,e,C.makeArray(n)):i.push(n)),i||[]},dequeue:function(t,e){e=e||"fx";var n=C.queue(t,e),i=n.length,o=n.shift(),s=C._queueHooks(t,e);"inprogress"===o&&(o=n.shift(),i--),o&&("fx"===e&&n.unshift("inprogress"),delete s.stop,o.call(t,(function(){C.dequeue(t,e)}),s)),!i&&s&&s.empty.fire()},_queueHooks:function(t,e){var n=e+"queueHooks";return at.get(t,n)||at.access(t,n,{empty:C.Callbacks("once memory").add((function(){at.remove(t,[e+"queue",n])}))})}}),C.fn.extend({queue:function(t,e){var n=2;return"string"!=typeof t&&(e=t,t="fx",n--),arguments.length<n?C.queue(this[0],t):void 0===e?this:this.each((function(){var n=C.queue(this,t,e);C._queueHooks(this,t),"fx"===t&&"inprogress"!==n[0]&&C.dequeue(this,t)}))},dequeue:function(t){return this.each((function(){C.dequeue(this,t)}))},clearQueue:function(t){return this.queue(t||"fx",[])},promise:function(t,e){var n,i=1,o=C.Deferred(),s=this,r=this.length,a=function(){--i||o.resolveWith(s,[s])};for("string"!=typeof t&&(e=t,t=void 0),t=t||"fx";r--;)(n=at.get(s[r],t+"queueHooks"))&&n.empty&&(i++,n.empty.add(a));return a(),o.promise(e)}});var ht=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,ft=new RegExp("^(?:([+-])=|)("+ht+")([a-z%]*)$","i"),pt=["Top","Right","Bottom","Left"],gt=y.documentElement,mt=function(t){return C.contains(t.ownerDocument,t)},vt={composed:!0};gt.getRootNode&&(mt=function(t){return C.contains(t.ownerDocument,t)||t.getRootNode(vt)===t.ownerDocument});var bt=function(t,e){return"none"===(t=e||t).style.display||""===t.style.display&&mt(t)&&"none"===C.css(t,"display")};function yt(t,e,n,i){var o,s,r=20,a=i?function(){return i.cur()}:function(){return C.css(t,e,"")},l=a(),c=n&&n[3]||(C.cssNumber[e]?"":"px"),u=t.nodeType&&(C.cssNumber[e]||"px"!==c&&+l)&&ft.exec(C.css(t,e));if(u&&u[3]!==c){for(l/=2,c=c||u[3],u=+l||1;r--;)C.style(t,e,u+c),(1-s)*(1-(s=a()/l||.5))<=0&&(r=0),u/=s;u*=2,C.style(t,e,u+c),n=n||[]}return n&&(u=+u||+l||0,o=n[1]?u+(n[1]+1)*n[2]:+n[2],i&&(i.unit=c,i.start=u,i.end=o)),o}var xt={};function wt(t){var e,n=t.ownerDocument,i=t.nodeName,o=xt[i];return o||(e=n.body.appendChild(n.createElement(i)),o=C.css(e,"display"),e.parentNode.removeChild(e),"none"===o&&(o="block"),xt[i]=o,o)}function Et(t,e){for(var n,i,o=[],s=0,r=t.length;s<r;s++)(i=t[s]).style&&(n=i.style.display,e?("none"===n&&(o[s]=at.get(i,"display")||null,o[s]||(i.style.display="")),""===i.style.display&&bt(i)&&(o[s]=wt(i))):"none"!==n&&(o[s]="none",at.set(i,"display",n)));for(s=0;s<r;s++)null!=o[s]&&(t[s].style.display=o[s]);return t}C.fn.extend({show:function(){return Et(this,!0)},hide:function(){return Et(this)},toggle:function(t){return"boolean"==typeof t?t?this.show():this.hide():this.each((function(){bt(this)?C(this).show():C(this).hide()}))}});var St,Tt,Ct=/^(?:checkbox|radio)$/i,_t=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,Pt=/^$|^module$|\/(?:java|ecma)script/i;St=y.createDocumentFragment().appendChild(y.createElement("div")),(Tt=y.createElement("input")).setAttribute("type","radio"),Tt.setAttribute("checked","checked"),Tt.setAttribute("name","t"),St.appendChild(Tt),m.checkClone=St.cloneNode(!0).cloneNode(!0).lastChild.checked,St.innerHTML="<textarea>x</textarea>",m.noCloneChecked=!!St.cloneNode(!0).lastChild.defaultValue,St.innerHTML="<option></option>",m.option=!!St.lastChild;var kt={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function Ot(t,e){var n;return n=void 0!==t.getElementsByTagName?t.getElementsByTagName(e||"*"):void 0!==t.querySelectorAll?t.querySelectorAll(e||"*"):[],void 0===e||e&&P(t,e)?C.merge([t],n):n}function Mt(t,e){for(var n=0,i=t.length;n<i;n++)at.set(t[n],"globalEval",!e||at.get(e[n],"globalEval"))}kt.tbody=kt.tfoot=kt.colgroup=kt.caption=kt.thead,kt.th=kt.td,m.option||(kt.optgroup=kt.option=[1,"<select multiple='multiple'>","</select>"]);var At=/<|&#?\w+;/;function Lt(t,e,n,i,o){for(var s,r,a,l,c,u,d=e.createDocumentFragment(),h=[],f=0,p=t.length;f<p;f++)if((s=t[f])||0===s)if("object"===E(s))C.merge(h,s.nodeType?[s]:s);else if(At.test(s)){for(r=r||d.appendChild(e.createElement("div")),a=(_t.exec(s)||["",""])[1].toLowerCase(),l=kt[a]||kt._default,r.innerHTML=l[1]+C.htmlPrefilter(s)+l[2],u=l[0];u--;)r=r.lastChild;C.merge(h,r.childNodes),(r=d.firstChild).textContent=""}else h.push(e.createTextNode(s));for(d.textContent="",f=0;s=h[f++];)if(i&&C.inArray(s,i)>-1)o&&o.push(s);else if(c=mt(s),r=Ot(d.appendChild(s),"script"),c&&Mt(r),n)for(u=0;s=r[u++];)Pt.test(s.type||"")&&n.push(s);return d}var zt=/^([^.]*)(?:\.(.+)|)/;function Dt(){return!0}function jt(){return!1}function It(t,e,n,i,o,s){var r,a;if("object"==typeof e){for(a in"string"!=typeof n&&(i=i||n,n=void 0),e)It(t,a,n,i,e[a],s);return t}if(null==i&&null==o?(o=n,i=n=void 0):null==o&&("string"==typeof n?(o=i,i=void 0):(o=i,i=n,n=void 0)),!1===o)o=jt;else if(!o)return t;return 1===s&&(r=o,o=function(t){return C().off(t),r.apply(this,arguments)},o.guid=r.guid||(r.guid=C.guid++)),t.each((function(){C.event.add(this,e,o,i,n)}))}function Rt(t,e,n){n?(at.set(t,e,!1),C.event.add(t,e,{namespace:!1,handler:function(t){var n,i=at.get(this,e);if(1&t.isTrigger&&this[e]){if(i)(C.event.special[e]||{}).delegateType&&t.stopPropagation();else if(i=a.call(arguments),at.set(this,e,i),this[e](),n=at.get(this,e),at.set(this,e,!1),i!==n)return t.stopImmediatePropagation(),t.preventDefault(),n}else i&&(at.set(this,e,C.event.trigger(i[0],i.slice(1),this)),t.stopPropagation(),t.isImmediatePropagationStopped=Dt)}})):void 0===at.get(t,e)&&C.event.add(t,e,Dt)}C.event={global:{},add:function(t,e,n,i,o){var s,r,a,l,c,u,d,h,f,p,g,m=at.get(t);if(st(t))for(n.handler&&(n=(s=n).handler,o=s.selector),o&&C.find.matchesSelector(gt,o),n.guid||(n.guid=C.guid++),(l=m.events)||(l=m.events=Object.create(null)),(r=m.handle)||(r=m.handle=function(e){return void 0!==C&&C.event.triggered!==e.type?C.event.dispatch.apply(t,arguments):void 0}),c=(e=(e||"").match(U)||[""]).length;c--;)f=g=(a=zt.exec(e[c])||[])[1],p=(a[2]||"").split(".").sort(),f&&(d=C.event.special[f]||{},f=(o?d.delegateType:d.bindType)||f,d=C.event.special[f]||{},u=C.extend({type:f,origType:g,data:i,handler:n,guid:n.guid,selector:o,needsContext:o&&C.expr.match.needsContext.test(o),namespace:p.join(".")},s),(h=l[f])||((h=l[f]=[]).delegateCount=0,d.setup&&!1!==d.setup.call(t,i,p,r)||t.addEventListener&&t.addEventListener(f,r)),d.add&&(d.add.call(t,u),u.handler.guid||(u.handler.guid=n.guid)),o?h.splice(h.delegateCount++,0,u):h.push(u),C.event.global[f]=!0)},remove:function(t,e,n,i,o){var s,r,a,l,c,u,d,h,f,p,g,m=at.hasData(t)&&at.get(t);if(m&&(l=m.events)){for(c=(e=(e||"").match(U)||[""]).length;c--;)if(f=g=(a=zt.exec(e[c])||[])[1],p=(a[2]||"").split(".").sort(),f){for(d=C.event.special[f]||{},h=l[f=(i?d.delegateType:d.bindType)||f]||[],a=a[2]&&new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)"),r=s=h.length;s--;)u=h[s],!o&&g!==u.origType||n&&n.guid!==u.guid||a&&!a.test(u.namespace)||i&&i!==u.selector&&("**"!==i||!u.selector)||(h.splice(s,1),u.selector&&h.delegateCount--,d.remove&&d.remove.call(t,u));r&&!h.length&&(d.teardown&&!1!==d.teardown.call(t,p,m.handle)||C.removeEvent(t,f,m.handle),delete l[f])}else for(f in l)C.event.remove(t,f+e[c],n,i,!0);C.isEmptyObject(l)&&at.remove(t,"handle events")}},dispatch:function(t){var e,n,i,o,s,r,a=new Array(arguments.length),l=C.event.fix(t),c=(at.get(this,"events")||Object.create(null))[l.type]||[],u=C.event.special[l.type]||{};for(a[0]=l,e=1;e<arguments.length;e++)a[e]=arguments[e];if(l.delegateTarget=this,!u.preDispatch||!1!==u.preDispatch.call(this,l)){for(r=C.event.handlers.call(this,l,c),e=0;(o=r[e++])&&!l.isPropagationStopped();)for(l.currentTarget=o.elem,n=0;(s=o.handlers[n++])&&!l.isImmediatePropagationStopped();)l.rnamespace&&!1!==s.namespace&&!l.rnamespace.test(s.namespace)||(l.handleObj=s,l.data=s.data,void 0!==(i=((C.event.special[s.origType]||{}).handle||s.handler).apply(o.elem,a))&&!1===(l.result=i)&&(l.preventDefault(),l.stopPropagation()));return u.postDispatch&&u.postDispatch.call(this,l),l.result}},handlers:function(t,e){var n,i,o,s,r,a=[],l=e.delegateCount,c=t.target;if(l&&c.nodeType&&!("click"===t.type&&t.button>=1))for(;c!==this;c=c.parentNode||this)if(1===c.nodeType&&("click"!==t.type||!0!==c.disabled)){for(s=[],r={},n=0;n<l;n++)void 0===r[o=(i=e[n]).selector+" "]&&(r[o]=i.needsContext?C(o,this).index(c)>-1:C.find(o,this,null,[c]).length),r[o]&&s.push(i);s.length&&a.push({elem:c,handlers:s})}return c=this,l<e.length&&a.push({elem:c,handlers:e.slice(l)}),a},addProp:function(t,e){Object.defineProperty(C.Event.prototype,t,{enumerable:!0,configurable:!0,get:v(e)?function(){if(this.originalEvent)return e(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[t]},set:function(e){Object.defineProperty(this,t,{enumerable:!0,configurable:!0,writable:!0,value:e})}})},fix:function(t){return t[C.expando]?t:new C.Event(t)},special:{load:{noBubble:!0},click:{setup:function(t){var e=this||t;return Ct.test(e.type)&&e.click&&P(e,"input")&&Rt(e,"click",!0),!1},trigger:function(t){var e=this||t;return Ct.test(e.type)&&e.click&&P(e,"input")&&Rt(e,"click"),!0},_default:function(t){var e=t.target;return Ct.test(e.type)&&e.click&&P(e,"input")&&at.get(e,"click")||P(e,"a")}},beforeunload:{postDispatch:function(t){void 0!==t.result&&t.originalEvent&&(t.originalEvent.returnValue=t.result)}}}},C.removeEvent=function(t,e,n){t.removeEventListener&&t.removeEventListener(e,n)},C.Event=function(t,e){if(!(this instanceof C.Event))return new C.Event(t,e);t&&t.type?(this.originalEvent=t,this.type=t.type,this.isDefaultPrevented=t.defaultPrevented||void 0===t.defaultPrevented&&!1===t.returnValue?Dt:jt,this.target=t.target&&3===t.target.nodeType?t.target.parentNode:t.target,this.currentTarget=t.currentTarget,this.relatedTarget=t.relatedTarget):this.type=t,e&&C.extend(this,e),this.timeStamp=t&&t.timeStamp||Date.now(),this[C.expando]=!0},C.Event.prototype={constructor:C.Event,isDefaultPrevented:jt,isPropagationStopped:jt,isImmediatePropagationStopped:jt,isSimulated:!1,preventDefault:function(){var t=this.originalEvent;this.isDefaultPrevented=Dt,t&&!this.isSimulated&&t.preventDefault()},stopPropagation:function(){var t=this.originalEvent;this.isPropagationStopped=Dt,t&&!this.isSimulated&&t.stopPropagation()},stopImmediatePropagation:function(){var t=this.originalEvent;this.isImmediatePropagationStopped=Dt,t&&!this.isSimulated&&t.stopImmediatePropagation(),this.stopPropagation()}},C.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},C.event.addProp),C.each({focus:"focusin",blur:"focusout"},(function(t,e){function n(t){if(y.documentMode){var n=at.get(this,"handle"),i=C.event.fix(t);i.type="focusin"===t.type?"focus":"blur",i.isSimulated=!0,n(t),i.target===i.currentTarget&&n(i)}else C.event.simulate(e,t.target,C.event.fix(t))}C.event.special[t]={setup:function(){var i;if(Rt(this,t,!0),!y.documentMode)return!1;(i=at.get(this,e))||this.addEventListener(e,n),at.set(this,e,(i||0)+1)},trigger:function(){return Rt(this,t),!0},teardown:function(){var t;if(!y.documentMode)return!1;(t=at.get(this,e)-1)?at.set(this,e,t):(this.removeEventListener(e,n),at.remove(this,e))},_default:function(e){return at.get(e.target,t)},delegateType:e},C.event.special[e]={setup:function(){var i=this.ownerDocument||this.document||this,o=y.documentMode?this:i,s=at.get(o,e);s||(y.documentMode?this.addEventListener(e,n):i.addEventListener(t,n,!0)),at.set(o,e,(s||0)+1)},teardown:function(){var i=this.ownerDocument||this.document||this,o=y.documentMode?this:i,s=at.get(o,e)-1;s?at.set(o,e,s):(y.documentMode?this.removeEventListener(e,n):i.removeEventListener(t,n,!0),at.remove(o,e))}}})),C.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},(function(t,e){C.event.special[t]={delegateType:e,bindType:e,handle:function(t){var n,i=t.relatedTarget,o=t.handleObj;return i&&(i===this||C.contains(this,i))||(t.type=o.origType,n=o.handler.apply(this,arguments),t.type=e),n}}})),C.fn.extend({on:function(t,e,n,i){return It(this,t,e,n,i)},one:function(t,e,n,i){return It(this,t,e,n,i,1)},off:function(t,e,n){var i,o;if(t&&t.preventDefault&&t.handleObj)return i=t.handleObj,C(t.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler),this;if("object"==typeof t){for(o in t)this.off(o,e,t[o]);return this}return!1!==e&&"function"!=typeof e||(n=e,e=void 0),!1===n&&(n=jt),this.each((function(){C.event.remove(this,t,n,e)}))}});var Ft=/<script|<style|<link/i,Nt=/checked\s*(?:[^=]|=\s*.checked.)/i,Ht=/^\s*<!\[CDATA\[|\]\]>\s*$/g;function Bt(t,e){return P(t,"table")&&P(11!==e.nodeType?e:e.firstChild,"tr")&&C(t).children("tbody")[0]||t}function qt(t){return t.type=(null!==t.getAttribute("type"))+"/"+t.type,t}function $t(t){return"true/"===(t.type||"").slice(0,5)?t.type=t.type.slice(5):t.removeAttribute("type"),t}function Wt(t,e){var n,i,o,s,r,a;if(1===e.nodeType){if(at.hasData(t)&&(a=at.get(t).events))for(o in at.remove(e,"handle events"),a)for(n=0,i=a[o].length;n<i;n++)C.event.add(e,o,a[o][n]);lt.hasData(t)&&(s=lt.access(t),r=C.extend({},s),lt.set(e,r))}}function Xt(t,e){var n=e.nodeName.toLowerCase();"input"===n&&Ct.test(t.type)?e.checked=t.checked:"input"!==n&&"textarea"!==n||(e.defaultValue=t.defaultValue)}function Yt(t,e,n,i){e=l(e);var o,s,r,a,c,u,d=0,h=t.length,f=h-1,p=e[0],g=v(p);if(g||h>1&&"string"==typeof p&&!m.checkClone&&Nt.test(p))return t.each((function(o){var s=t.eq(o);g&&(e[0]=p.call(this,o,s.html())),Yt(s,e,n,i)}));if(h&&(s=(o=Lt(e,t[0].ownerDocument,!1,t,i)).firstChild,1===o.childNodes.length&&(o=s),s||i)){for(a=(r=C.map(Ot(o,"script"),qt)).length;d<h;d++)c=o,d!==f&&(c=C.clone(c,!0,!0),a&&C.merge(r,Ot(c,"script"))),n.call(t[d],c,d);if(a)for(u=r[r.length-1].ownerDocument,C.map(r,$t),d=0;d<a;d++)c=r[d],Pt.test(c.type||"")&&!at.access(c,"globalEval")&&C.contains(u,c)&&(c.src&&"module"!==(c.type||"").toLowerCase()?C._evalUrl&&!c.noModule&&C._evalUrl(c.src,{nonce:c.nonce||c.getAttribute("nonce")},u):w(c.textContent.replace(Ht,""),c,u))}return t}function Ut(t,e,n){for(var i,o=e?C.filter(e,t):t,s=0;null!=(i=o[s]);s++)n||1!==i.nodeType||C.cleanData(Ot(i)),i.parentNode&&(n&&mt(i)&&Mt(Ot(i,"script")),i.parentNode.removeChild(i));return t}C.extend({htmlPrefilter:function(t){return t},clone:function(t,e,n){var i,o,s,r,a=t.cloneNode(!0),l=mt(t);if(!(m.noCloneChecked||1!==t.nodeType&&11!==t.nodeType||C.isXMLDoc(t)))for(r=Ot(a),i=0,o=(s=Ot(t)).length;i<o;i++)Xt(s[i],r[i]);if(e)if(n)for(s=s||Ot(t),r=r||Ot(a),i=0,o=s.length;i<o;i++)Wt(s[i],r[i]);else Wt(t,a);return(r=Ot(a,"script")).length>0&&Mt(r,!l&&Ot(t,"script")),a},cleanData:function(t){for(var e,n,i,o=C.event.special,s=0;void 0!==(n=t[s]);s++)if(st(n)){if(e=n[at.expando]){if(e.events)for(i in e.events)o[i]?C.event.remove(n,i):C.removeEvent(n,i,e.handle);n[at.expando]=void 0}n[lt.expando]&&(n[lt.expando]=void 0)}}}),C.fn.extend({detach:function(t){return Ut(this,t,!0)},remove:function(t){return Ut(this,t)},text:function(t){return tt(this,(function(t){return void 0===t?C.text(this):this.empty().each((function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=t)}))}),null,t,arguments.length)},append:function(){return Yt(this,arguments,(function(t){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||Bt(this,t).appendChild(t)}))},prepend:function(){return Yt(this,arguments,(function(t){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var e=Bt(this,t);e.insertBefore(t,e.firstChild)}}))},before:function(){return Yt(this,arguments,(function(t){this.parentNode&&this.parentNode.insertBefore(t,this)}))},after:function(){return Yt(this,arguments,(function(t){this.parentNode&&this.parentNode.insertBefore(t,this.nextSibling)}))},empty:function(){for(var t,e=0;null!=(t=this[e]);e++)1===t.nodeType&&(C.cleanData(Ot(t,!1)),t.textContent="");return this},clone:function(t,e){return t=null!=t&&t,e=null==e?t:e,this.map((function(){return C.clone(this,t,e)}))},html:function(t){return tt(this,(function(t){var e=this[0]||{},n=0,i=this.length;if(void 0===t&&1===e.nodeType)return e.innerHTML;if("string"==typeof t&&!Ft.test(t)&&!kt[(_t.exec(t)||["",""])[1].toLowerCase()]){t=C.htmlPrefilter(t);try{for(;n<i;n++)1===(e=this[n]||{}).nodeType&&(C.cleanData(Ot(e,!1)),e.innerHTML=t);e=0}catch(t){}}e&&this.empty().append(t)}),null,t,arguments.length)},replaceWith:function(){var t=[];return Yt(this,arguments,(function(e){var n=this.parentNode;C.inArray(this,t)<0&&(C.cleanData(Ot(this)),n&&n.replaceChild(e,this))}),t)}}),C.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},(function(t,e){C.fn[t]=function(t){for(var n,i=[],o=C(t),s=o.length-1,r=0;r<=s;r++)n=r===s?this:this.clone(!0),C(o[r])[e](n),c.apply(i,n.get());return this.pushStack(i)}}));var Vt=new RegExp("^("+ht+")(?!px)[a-z%]+$","i"),Zt=/^--/,Gt=function(t){var e=t.ownerDocument.defaultView;return e&&e.opener||(e=i),e.getComputedStyle(t)},Kt=function(t,e,n){var i,o,s={};for(o in e)s[o]=t.style[o],t.style[o]=e[o];for(o in i=n.call(t),e)t.style[o]=s[o];return i},Jt=new RegExp(pt.join("|"),"i");function Qt(t,e,n){var i,o,s,r,a=Zt.test(e),l=t.style;return(n=n||Gt(t))&&(r=n.getPropertyValue(e)||n[e],a&&r&&(r=r.replace(L,"$1")||void 0),""!==r||mt(t)||(r=C.style(t,e)),!m.pixelBoxStyles()&&Vt.test(r)&&Jt.test(e)&&(i=l.width,o=l.minWidth,s=l.maxWidth,l.minWidth=l.maxWidth=l.width=r,r=n.width,l.width=i,l.minWidth=o,l.maxWidth=s)),void 0!==r?r+"":r}function te(t,e){return{get:function(){if(!t())return(this.get=e).apply(this,arguments);delete this.get}}}!function(){function t(){if(u){c.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",u.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",gt.appendChild(c).appendChild(u);var t=i.getComputedStyle(u);n="1%"!==t.top,l=12===e(t.marginLeft),u.style.right="60%",r=36===e(t.right),o=36===e(t.width),u.style.position="absolute",s=12===e(u.offsetWidth/3),gt.removeChild(c),u=null}}function e(t){return Math.round(parseFloat(t))}var n,o,s,r,a,l,c=y.createElement("div"),u=y.createElement("div");u.style&&(u.style.backgroundClip="content-box",u.cloneNode(!0).style.backgroundClip="",m.clearCloneStyle="content-box"===u.style.backgroundClip,C.extend(m,{boxSizingReliable:function(){return t(),o},pixelBoxStyles:function(){return t(),r},pixelPosition:function(){return t(),n},reliableMarginLeft:function(){return t(),l},scrollboxSize:function(){return t(),s},reliableTrDimensions:function(){var t,e,n,o;return null==a&&(t=y.createElement("table"),e=y.createElement("tr"),n=y.createElement("div"),t.style.cssText="position:absolute;left:-11111px;border-collapse:separate",e.style.cssText="box-sizing:content-box;border:1px solid",e.style.height="1px",n.style.height="9px",n.style.display="block",gt.appendChild(t).appendChild(e).appendChild(n),o=i.getComputedStyle(e),a=parseInt(o.height,10)+parseInt(o.borderTopWidth,10)+parseInt(o.borderBottomWidth,10)===e.offsetHeight,gt.removeChild(t)),a}}))}();var ee=["Webkit","Moz","ms"],ne=y.createElement("div").style,ie={};function oe(t){return C.cssProps[t]||ie[t]||(t in ne?t:ie[t]=function(t){for(var e=t[0].toUpperCase()+t.slice(1),n=ee.length;n--;)if((t=ee[n]+e)in ne)return t}(t)||t)}var se=/^(none|table(?!-c[ea]).+)/,re={position:"absolute",visibility:"hidden",display:"block"},ae={letterSpacing:"0",fontWeight:"400"};function le(t,e,n){var i=ft.exec(e);return i?Math.max(0,i[2]-(n||0))+(i[3]||"px"):e}function ce(t,e,n,i,o,s){var r="width"===e?1:0,a=0,l=0,c=0;if(n===(i?"border":"content"))return 0;for(;r<4;r+=2)"margin"===n&&(c+=C.css(t,n+pt[r],!0,o)),i?("content"===n&&(l-=C.css(t,"padding"+pt[r],!0,o)),"margin"!==n&&(l-=C.css(t,"border"+pt[r]+"Width",!0,o))):(l+=C.css(t,"padding"+pt[r],!0,o),"padding"!==n?l+=C.css(t,"border"+pt[r]+"Width",!0,o):a+=C.css(t,"border"+pt[r]+"Width",!0,o));return!i&&s>=0&&(l+=Math.max(0,Math.ceil(t["offset"+e[0].toUpperCase()+e.slice(1)]-s-l-a-.5))||0),l+c}function ue(t,e,n){var i=Gt(t),o=(!m.boxSizingReliable()||n)&&"border-box"===C.css(t,"boxSizing",!1,i),s=o,r=Qt(t,e,i),a="offset"+e[0].toUpperCase()+e.slice(1);if(Vt.test(r)){if(!n)return r;r="auto"}return(!m.boxSizingReliable()&&o||!m.reliableTrDimensions()&&P(t,"tr")||"auto"===r||!parseFloat(r)&&"inline"===C.css(t,"display",!1,i))&&t.getClientRects().length&&(o="border-box"===C.css(t,"boxSizing",!1,i),(s=a in t)&&(r=t[a])),(r=parseFloat(r)||0)+ce(t,e,n||(o?"border":"content"),s,i,r)+"px"}function de(t,e,n,i,o){return new de.prototype.init(t,e,n,i,o)}C.extend({cssHooks:{opacity:{get:function(t,e){if(e){var n=Qt(t,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,aspectRatio:!0,borderImageSlice:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,scale:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeMiterlimit:!0,strokeOpacity:!0},cssProps:{},style:function(t,e,n,i){if(t&&3!==t.nodeType&&8!==t.nodeType&&t.style){var o,s,r,a=ot(e),l=Zt.test(e),c=t.style;if(l||(e=oe(a)),r=C.cssHooks[e]||C.cssHooks[a],void 0===n)return r&&"get"in r&&void 0!==(o=r.get(t,!1,i))?o:c[e];"string"==(s=typeof n)&&(o=ft.exec(n))&&o[1]&&(n=yt(t,e,o),s="number"),null!=n&&n==n&&("number"!==s||l||(n+=o&&o[3]||(C.cssNumber[a]?"":"px")),m.clearCloneStyle||""!==n||0!==e.indexOf("background")||(c[e]="inherit"),r&&"set"in r&&void 0===(n=r.set(t,n,i))||(l?c.setProperty(e,n):c[e]=n))}},css:function(t,e,n,i){var o,s,r,a=ot(e);return Zt.test(e)||(e=oe(a)),(r=C.cssHooks[e]||C.cssHooks[a])&&"get"in r&&(o=r.get(t,!0,n)),void 0===o&&(o=Qt(t,e,i)),"normal"===o&&e in ae&&(o=ae[e]),""===n||n?(s=parseFloat(o),!0===n||isFinite(s)?s||0:o):o}}),C.each(["height","width"],(function(t,e){C.cssHooks[e]={get:function(t,n,i){if(n)return!se.test(C.css(t,"display"))||t.getClientRects().length&&t.getBoundingClientRect().width?ue(t,e,i):Kt(t,re,(function(){return ue(t,e,i)}))},set:function(t,n,i){var o,s=Gt(t),r=!m.scrollboxSize()&&"absolute"===s.position,a=(r||i)&&"border-box"===C.css(t,"boxSizing",!1,s),l=i?ce(t,e,i,a,s):0;return a&&r&&(l-=Math.ceil(t["offset"+e[0].toUpperCase()+e.slice(1)]-parseFloat(s[e])-ce(t,e,"border",!1,s)-.5)),l&&(o=ft.exec(n))&&"px"!==(o[3]||"px")&&(t.style[e]=n,n=C.css(t,e)),le(0,n,l)}}})),C.cssHooks.marginLeft=te(m.reliableMarginLeft,(function(t,e){if(e)return(parseFloat(Qt(t,"marginLeft"))||t.getBoundingClientRect().left-Kt(t,{marginLeft:0},(function(){return t.getBoundingClientRect().left})))+"px"})),C.each({margin:"",padding:"",border:"Width"},(function(t,e){C.cssHooks[t+e]={expand:function(n){for(var i=0,o={},s="string"==typeof n?n.split(" "):[n];i<4;i++)o[t+pt[i]+e]=s[i]||s[i-2]||s[0];return o}},"margin"!==t&&(C.cssHooks[t+e].set=le)})),C.fn.extend({css:function(t,e){return tt(this,(function(t,e,n){var i,o,s={},r=0;if(Array.isArray(e)){for(i=Gt(t),o=e.length;r<o;r++)s[e[r]]=C.css(t,e[r],!1,i);return s}return void 0!==n?C.style(t,e,n):C.css(t,e)}),t,e,arguments.length>1)}}),C.Tween=de,de.prototype={constructor:de,init:function(t,e,n,i,o,s){this.elem=t,this.prop=n,this.easing=o||C.easing._default,this.options=e,this.start=this.now=this.cur(),this.end=i,this.unit=s||(C.cssNumber[n]?"":"px")},cur:function(){var t=de.propHooks[this.prop];return t&&t.get?t.get(this):de.propHooks._default.get(this)},run:function(t){var e,n=de.propHooks[this.prop];return this.options.duration?this.pos=e=C.easing[this.easing](t,this.options.duration*t,0,1,this.options.duration):this.pos=e=t,this.now=(this.end-this.start)*e+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):de.propHooks._default.set(this),this}},de.prototype.init.prototype=de.prototype,de.propHooks={_default:{get:function(t){var e;return 1!==t.elem.nodeType||null!=t.elem[t.prop]&&null==t.elem.style[t.prop]?t.elem[t.prop]:(e=C.css(t.elem,t.prop,""))&&"auto"!==e?e:0},set:function(t){C.fx.step[t.prop]?C.fx.step[t.prop](t):1!==t.elem.nodeType||!C.cssHooks[t.prop]&&null==t.elem.style[oe(t.prop)]?t.elem[t.prop]=t.now:C.style(t.elem,t.prop,t.now+t.unit)}}},de.propHooks.scrollTop=de.propHooks.scrollLeft={set:function(t){t.elem.nodeType&&t.elem.parentNode&&(t.elem[t.prop]=t.now)}},C.easing={linear:function(t){return t},swing:function(t){return.5-Math.cos(t*Math.PI)/2},_default:"swing"},C.fx=de.prototype.init,C.fx.step={};var he,fe,pe=/^(?:toggle|show|hide)$/,ge=/queueHooks$/;function me(){fe&&(!1===y.hidden&&i.requestAnimationFrame?i.requestAnimationFrame(me):i.setTimeout(me,C.fx.interval),C.fx.tick())}function ve(){return i.setTimeout((function(){he=void 0})),he=Date.now()}function be(t,e){var n,i=0,o={height:t};for(e=e?1:0;i<4;i+=2-e)o["margin"+(n=pt[i])]=o["padding"+n]=t;return e&&(o.opacity=o.width=t),o}function ye(t,e,n){for(var i,o=(xe.tweeners[e]||[]).concat(xe.tweeners["*"]),s=0,r=o.length;s<r;s++)if(i=o[s].call(n,e,t))return i}function xe(t,e,n){var i,o,s=0,r=xe.prefilters.length,a=C.Deferred().always((function(){delete l.elem})),l=function(){if(o)return!1;for(var e=he||ve(),n=Math.max(0,c.startTime+c.duration-e),i=1-(n/c.duration||0),s=0,r=c.tweens.length;s<r;s++)c.tweens[s].run(i);return a.notifyWith(t,[c,i,n]),i<1&&r?n:(r||a.notifyWith(t,[c,1,0]),a.resolveWith(t,[c]),!1)},c=a.promise({elem:t,props:C.extend({},e),opts:C.extend(!0,{specialEasing:{},easing:C.easing._default},n),originalProperties:e,originalOptions:n,startTime:he||ve(),duration:n.duration,tweens:[],createTween:function(e,n){var i=C.Tween(t,c.opts,e,n,c.opts.specialEasing[e]||c.opts.easing);return c.tweens.push(i),i},stop:function(e){var n=0,i=e?c.tweens.length:0;if(o)return this;for(o=!0;n<i;n++)c.tweens[n].run(1);return e?(a.notifyWith(t,[c,1,0]),a.resolveWith(t,[c,e])):a.rejectWith(t,[c,e]),this}}),u=c.props;for(function(t,e){var n,i,o,s,r;for(n in t)if(o=e[i=ot(n)],s=t[n],Array.isArray(s)&&(o=s[1],s=t[n]=s[0]),n!==i&&(t[i]=s,delete t[n]),(r=C.cssHooks[i])&&"expand"in r)for(n in s=r.expand(s),delete t[i],s)n in t||(t[n]=s[n],e[n]=o);else e[i]=o}(u,c.opts.specialEasing);s<r;s++)if(i=xe.prefilters[s].call(c,t,u,c.opts))return v(i.stop)&&(C._queueHooks(c.elem,c.opts.queue).stop=i.stop.bind(i)),i;return C.map(u,ye,c),v(c.opts.start)&&c.opts.start.call(t,c),c.progress(c.opts.progress).done(c.opts.done,c.opts.complete).fail(c.opts.fail).always(c.opts.always),C.fx.timer(C.extend(l,{elem:t,anim:c,queue:c.opts.queue})),c}C.Animation=C.extend(xe,{tweeners:{"*":[function(t,e){var n=this.createTween(t,e);return yt(n.elem,t,ft.exec(e),n),n}]},tweener:function(t,e){v(t)?(e=t,t=["*"]):t=t.match(U);for(var n,i=0,o=t.length;i<o;i++)n=t[i],xe.tweeners[n]=xe.tweeners[n]||[],xe.tweeners[n].unshift(e)},prefilters:[function(t,e,n){var i,o,s,r,a,l,c,u,d="width"in e||"height"in e,h=this,f={},p=t.style,g=t.nodeType&&bt(t),m=at.get(t,"fxshow");for(i in n.queue||(null==(r=C._queueHooks(t,"fx")).unqueued&&(r.unqueued=0,a=r.empty.fire,r.empty.fire=function(){r.unqueued||a()}),r.unqueued++,h.always((function(){h.always((function(){r.unqueued--,C.queue(t,"fx").length||r.empty.fire()}))}))),e)if(o=e[i],pe.test(o)){if(delete e[i],s=s||"toggle"===o,o===(g?"hide":"show")){if("show"!==o||!m||void 0===m[i])continue;g=!0}f[i]=m&&m[i]||C.style(t,i)}if((l=!C.isEmptyObject(e))||!C.isEmptyObject(f))for(i in d&&1===t.nodeType&&(n.overflow=[p.overflow,p.overflowX,p.overflowY],null==(c=m&&m.display)&&(c=at.get(t,"display")),"none"===(u=C.css(t,"display"))&&(c?u=c:(Et([t],!0),c=t.style.display||c,u=C.css(t,"display"),Et([t]))),("inline"===u||"inline-block"===u&&null!=c)&&"none"===C.css(t,"float")&&(l||(h.done((function(){p.display=c})),null==c&&(u=p.display,c="none"===u?"":u)),p.display="inline-block")),n.overflow&&(p.overflow="hidden",h.always((function(){p.overflow=n.overflow[0],p.overflowX=n.overflow[1],p.overflowY=n.overflow[2]}))),l=!1,f)l||(m?"hidden"in m&&(g=m.hidden):m=at.access(t,"fxshow",{display:c}),s&&(m.hidden=!g),g&&Et([t],!0),h.done((function(){for(i in g||Et([t]),at.remove(t,"fxshow"),f)C.style(t,i,f[i])}))),l=ye(g?m[i]:0,i,h),i in m||(m[i]=l.start,g&&(l.end=l.start,l.start=0))}],prefilter:function(t,e){e?xe.prefilters.unshift(t):xe.prefilters.push(t)}}),C.speed=function(t,e,n){var i=t&&"object"==typeof t?C.extend({},t):{complete:n||!n&&e||v(t)&&t,duration:t,easing:n&&e||e&&!v(e)&&e};return C.fx.off?i.duration=0:"number"!=typeof i.duration&&(i.duration in C.fx.speeds?i.duration=C.fx.speeds[i.duration]:i.duration=C.fx.speeds._default),null!=i.queue&&!0!==i.queue||(i.queue="fx"),i.old=i.complete,i.complete=function(){v(i.old)&&i.old.call(this),i.queue&&C.dequeue(this,i.queue)},i},C.fn.extend({fadeTo:function(t,e,n,i){return this.filter(bt).css("opacity",0).show().end().animate({opacity:e},t,n,i)},animate:function(t,e,n,i){var o=C.isEmptyObject(t),s=C.speed(e,n,i),r=function(){var e=xe(this,C.extend({},t),s);(o||at.get(this,"finish"))&&e.stop(!0)};return r.finish=r,o||!1===s.queue?this.each(r):this.queue(s.queue,r)},stop:function(t,e,n){var i=function(t){var e=t.stop;delete t.stop,e(n)};return"string"!=typeof t&&(n=e,e=t,t=void 0),e&&this.queue(t||"fx",[]),this.each((function(){var e=!0,o=null!=t&&t+"queueHooks",s=C.timers,r=at.get(this);if(o)r[o]&&r[o].stop&&i(r[o]);else for(o in r)r[o]&&r[o].stop&&ge.test(o)&&i(r[o]);for(o=s.length;o--;)s[o].elem!==this||null!=t&&s[o].queue!==t||(s[o].anim.stop(n),e=!1,s.splice(o,1));!e&&n||C.dequeue(this,t)}))},finish:function(t){return!1!==t&&(t=t||"fx"),this.each((function(){var e,n=at.get(this),i=n[t+"queue"],o=n[t+"queueHooks"],s=C.timers,r=i?i.length:0;for(n.finish=!0,C.queue(this,t,[]),o&&o.stop&&o.stop.call(this,!0),e=s.length;e--;)s[e].elem===this&&s[e].queue===t&&(s[e].anim.stop(!0),s.splice(e,1));for(e=0;e<r;e++)i[e]&&i[e].finish&&i[e].finish.call(this);delete n.finish}))}}),C.each(["toggle","show","hide"],(function(t,e){var n=C.fn[e];C.fn[e]=function(t,i,o){return null==t||"boolean"==typeof t?n.apply(this,arguments):this.animate(be(e,!0),t,i,o)}})),C.each({slideDown:be("show"),slideUp:be("hide"),slideToggle:be("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},(function(t,e){C.fn[t]=function(t,n,i){return this.animate(e,t,n,i)}})),C.timers=[],C.fx.tick=function(){var t,e=0,n=C.timers;for(he=Date.now();e<n.length;e++)(t=n[e])()||n[e]!==t||n.splice(e--,1);n.length||C.fx.stop(),he=void 0},C.fx.timer=function(t){C.timers.push(t),C.fx.start()},C.fx.interval=13,C.fx.start=function(){fe||(fe=!0,me())},C.fx.stop=function(){fe=null},C.fx.speeds={slow:600,fast:200,_default:400},C.fn.delay=function(t,e){return t=C.fx&&C.fx.speeds[t]||t,e=e||"fx",this.queue(e,(function(e,n){var o=i.setTimeout(e,t);n.stop=function(){i.clearTimeout(o)}}))},function(){var t=y.createElement("input"),e=y.createElement("select").appendChild(y.createElement("option"));t.type="checkbox",m.checkOn=""!==t.value,m.optSelected=e.selected,(t=y.createElement("input")).value="t",t.type="radio",m.radioValue="t"===t.value}();var we,Ee=C.expr.attrHandle;C.fn.extend({attr:function(t,e){return tt(this,C.attr,t,e,arguments.length>1)},removeAttr:function(t){return this.each((function(){C.removeAttr(this,t)}))}}),C.extend({attr:function(t,e,n){var i,o,s=t.nodeType;if(3!==s&&8!==s&&2!==s)return void 0===t.getAttribute?C.prop(t,e,n):(1===s&&C.isXMLDoc(t)||(o=C.attrHooks[e.toLowerCase()]||(C.expr.match.bool.test(e)?we:void 0)),void 0!==n?null===n?void C.removeAttr(t,e):o&&"set"in o&&void 0!==(i=o.set(t,n,e))?i:(t.setAttribute(e,n+""),n):o&&"get"in o&&null!==(i=o.get(t,e))?i:null==(i=C.find.attr(t,e))?void 0:i)},attrHooks:{type:{set:function(t,e){if(!m.radioValue&&"radio"===e&&P(t,"input")){var n=t.value;return t.setAttribute("type",e),n&&(t.value=n),e}}}},removeAttr:function(t,e){var n,i=0,o=e&&e.match(U);if(o&&1===t.nodeType)for(;n=o[i++];)t.removeAttribute(n)}}),we={set:function(t,e,n){return!1===e?C.removeAttr(t,n):t.setAttribute(n,n),n}},C.each(C.expr.match.bool.source.match(/\w+/g),(function(t,e){var n=Ee[e]||C.find.attr;Ee[e]=function(t,e,i){var o,s,r=e.toLowerCase();return i||(s=Ee[r],Ee[r]=o,o=null!=n(t,e,i)?r:null,Ee[r]=s),o}}));var Se=/^(?:input|select|textarea|button)$/i,Te=/^(?:a|area)$/i;function Ce(t){return(t.match(U)||[]).join(" ")}function _e(t){return t.getAttribute&&t.getAttribute("class")||""}function Pe(t){return Array.isArray(t)?t:"string"==typeof t&&t.match(U)||[]}C.fn.extend({prop:function(t,e){return tt(this,C.prop,t,e,arguments.length>1)},removeProp:function(t){return this.each((function(){delete this[C.propFix[t]||t]}))}}),C.extend({prop:function(t,e,n){var i,o,s=t.nodeType;if(3!==s&&8!==s&&2!==s)return 1===s&&C.isXMLDoc(t)||(e=C.propFix[e]||e,o=C.propHooks[e]),void 0!==n?o&&"set"in o&&void 0!==(i=o.set(t,n,e))?i:t[e]=n:o&&"get"in o&&null!==(i=o.get(t,e))?i:t[e]},propHooks:{tabIndex:{get:function(t){var e=C.find.attr(t,"tabindex");return e?parseInt(e,10):Se.test(t.nodeName)||Te.test(t.nodeName)&&t.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),m.optSelected||(C.propHooks.selected={get:function(t){var e=t.parentNode;return e&&e.parentNode&&e.parentNode.selectedIndex,null},set:function(t){var e=t.parentNode;e&&(e.selectedIndex,e.parentNode&&e.parentNode.selectedIndex)}}),C.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],(function(){C.propFix[this.toLowerCase()]=this})),C.fn.extend({addClass:function(t){var e,n,i,o,s,r;return v(t)?this.each((function(e){C(this).addClass(t.call(this,e,_e(this)))})):(e=Pe(t)).length?this.each((function(){if(i=_e(this),n=1===this.nodeType&&" "+Ce(i)+" "){for(s=0;s<e.length;s++)o=e[s],n.indexOf(" "+o+" ")<0&&(n+=o+" ");r=Ce(n),i!==r&&this.setAttribute("class",r)}})):this},removeClass:function(t){var e,n,i,o,s,r;return v(t)?this.each((function(e){C(this).removeClass(t.call(this,e,_e(this)))})):arguments.length?(e=Pe(t)).length?this.each((function(){if(i=_e(this),n=1===this.nodeType&&" "+Ce(i)+" "){for(s=0;s<e.length;s++)for(o=e[s];n.indexOf(" "+o+" ")>-1;)n=n.replace(" "+o+" "," ");r=Ce(n),i!==r&&this.setAttribute("class",r)}})):this:this.attr("class","")},toggleClass:function(t,e){var n,i,o,s,r=typeof t,a="string"===r||Array.isArray(t);return v(t)?this.each((function(n){C(this).toggleClass(t.call(this,n,_e(this),e),e)})):"boolean"==typeof e&&a?e?this.addClass(t):this.removeClass(t):(n=Pe(t),this.each((function(){if(a)for(s=C(this),o=0;o<n.length;o++)i=n[o],s.hasClass(i)?s.removeClass(i):s.addClass(i);else void 0!==t&&"boolean"!==r||((i=_e(this))&&at.set(this,"__className__",i),this.setAttribute&&this.setAttribute("class",i||!1===t?"":at.get(this,"__className__")||""))})))},hasClass:function(t){var e,n,i=0;for(e=" "+t+" ";n=this[i++];)if(1===n.nodeType&&(" "+Ce(_e(n))+" ").indexOf(e)>-1)return!0;return!1}});var ke=/\r/g;C.fn.extend({val:function(t){var e,n,i,o=this[0];return arguments.length?(i=v(t),this.each((function(n){var o;1===this.nodeType&&(null==(o=i?t.call(this,n,C(this).val()):t)?o="":"number"==typeof o?o+="":Array.isArray(o)&&(o=C.map(o,(function(t){return null==t?"":t+""}))),(e=C.valHooks[this.type]||C.valHooks[this.nodeName.toLowerCase()])&&"set"in e&&void 0!==e.set(this,o,"value")||(this.value=o))}))):o?(e=C.valHooks[o.type]||C.valHooks[o.nodeName.toLowerCase()])&&"get"in e&&void 0!==(n=e.get(o,"value"))?n:"string"==typeof(n=o.value)?n.replace(ke,""):null==n?"":n:void 0}}),C.extend({valHooks:{option:{get:function(t){var e=C.find.attr(t,"value");return null!=e?e:Ce(C.text(t))}},select:{get:function(t){var e,n,i,o=t.options,s=t.selectedIndex,r="select-one"===t.type,a=r?null:[],l=r?s+1:o.length;for(i=s<0?l:r?s:0;i<l;i++)if(((n=o[i]).selected||i===s)&&!n.disabled&&(!n.parentNode.disabled||!P(n.parentNode,"optgroup"))){if(e=C(n).val(),r)return e;a.push(e)}return a},set:function(t,e){for(var n,i,o=t.options,s=C.makeArray(e),r=o.length;r--;)((i=o[r]).selected=C.inArray(C.valHooks.option.get(i),s)>-1)&&(n=!0);return n||(t.selectedIndex=-1),s}}}}),C.each(["radio","checkbox"],(function(){C.valHooks[this]={set:function(t,e){if(Array.isArray(e))return t.checked=C.inArray(C(t).val(),e)>-1}},m.checkOn||(C.valHooks[this].get=function(t){return null===t.getAttribute("value")?"on":t.value})}));var Oe=i.location,Me={guid:Date.now()},Ae=/\?/;C.parseXML=function(t){var e,n;if(!t||"string"!=typeof t)return null;try{e=(new i.DOMParser).parseFromString(t,"text/xml")}catch(t){}return n=e&&e.getElementsByTagName("parsererror")[0],e&&!n||C.error("Invalid XML: "+(n?C.map(n.childNodes,(function(t){return t.textContent})).join("\n"):t)),e};var Le=/^(?:focusinfocus|focusoutblur)$/,ze=function(t){t.stopPropagation()};C.extend(C.event,{trigger:function(t,e,n,o){var s,r,a,l,c,u,d,h,p=[n||y],g=f.call(t,"type")?t.type:t,m=f.call(t,"namespace")?t.namespace.split("."):[];if(r=h=a=n=n||y,3!==n.nodeType&&8!==n.nodeType&&!Le.test(g+C.event.triggered)&&(g.indexOf(".")>-1&&(m=g.split("."),g=m.shift(),m.sort()),c=g.indexOf(":")<0&&"on"+g,(t=t[C.expando]?t:new C.Event(g,"object"==typeof t&&t)).isTrigger=o?2:3,t.namespace=m.join("."),t.rnamespace=t.namespace?new RegExp("(^|\\.)"+m.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,t.result=void 0,t.target||(t.target=n),e=null==e?[t]:C.makeArray(e,[t]),d=C.event.special[g]||{},o||!d.trigger||!1!==d.trigger.apply(n,e))){if(!o&&!d.noBubble&&!b(n)){for(l=d.delegateType||g,Le.test(l+g)||(r=r.parentNode);r;r=r.parentNode)p.push(r),a=r;a===(n.ownerDocument||y)&&p.push(a.defaultView||a.parentWindow||i)}for(s=0;(r=p[s++])&&!t.isPropagationStopped();)h=r,t.type=s>1?l:d.bindType||g,(u=(at.get(r,"events")||Object.create(null))[t.type]&&at.get(r,"handle"))&&u.apply(r,e),(u=c&&r[c])&&u.apply&&st(r)&&(t.result=u.apply(r,e),!1===t.result&&t.preventDefault());return t.type=g,o||t.isDefaultPrevented()||d._default&&!1!==d._default.apply(p.pop(),e)||!st(n)||c&&v(n[g])&&!b(n)&&((a=n[c])&&(n[c]=null),C.event.triggered=g,t.isPropagationStopped()&&h.addEventListener(g,ze),n[g](),t.isPropagationStopped()&&h.removeEventListener(g,ze),C.event.triggered=void 0,a&&(n[c]=a)),t.result}},simulate:function(t,e,n){var i=C.extend(new C.Event,n,{type:t,isSimulated:!0});C.event.trigger(i,null,e)}}),C.fn.extend({trigger:function(t,e){return this.each((function(){C.event.trigger(t,e,this)}))},triggerHandler:function(t,e){var n=this[0];if(n)return C.event.trigger(t,e,n,!0)}});var De=/\[\]$/,je=/\r?\n/g,Ie=/^(?:submit|button|image|reset|file)$/i,Re=/^(?:input|select|textarea|keygen)/i;function Fe(t,e,n,i){var o;if(Array.isArray(e))C.each(e,(function(e,o){n||De.test(t)?i(t,o):Fe(t+"["+("object"==typeof o&&null!=o?e:"")+"]",o,n,i)}));else if(n||"object"!==E(e))i(t,e);else for(o in e)Fe(t+"["+o+"]",e[o],n,i)}C.param=function(t,e){var n,i=[],o=function(t,e){var n=v(e)?e():e;i[i.length]=encodeURIComponent(t)+"="+encodeURIComponent(null==n?"":n)};if(null==t)return"";if(Array.isArray(t)||t.jquery&&!C.isPlainObject(t))C.each(t,(function(){o(this.name,this.value)}));else for(n in t)Fe(n,t[n],e,o);return i.join("&")},C.fn.extend({serialize:function(){return C.param(this.serializeArray())},serializeArray:function(){return this.map((function(){var t=C.prop(this,"elements");return t?C.makeArray(t):this})).filter((function(){var t=this.type;return this.name&&!C(this).is(":disabled")&&Re.test(this.nodeName)&&!Ie.test(t)&&(this.checked||!Ct.test(t))})).map((function(t,e){var n=C(this).val();return null==n?null:Array.isArray(n)?C.map(n,(function(t){return{name:e.name,value:t.replace(je,"\r\n")}})):{name:e.name,value:n.replace(je,"\r\n")}})).get()}});var Ne=/%20/g,He=/#.*$/,Be=/([?&])_=[^&]*/,qe=/^(.*?):[ \t]*([^\r\n]*)$/gm,$e=/^(?:GET|HEAD)$/,We=/^\/\//,Xe={},Ye={},Ue="*/".concat("*"),Ve=y.createElement("a");function Ze(t){return function(e,n){"string"!=typeof e&&(n=e,e="*");var i,o=0,s=e.toLowerCase().match(U)||[];if(v(n))for(;i=s[o++];)"+"===i[0]?(i=i.slice(1)||"*",(t[i]=t[i]||[]).unshift(n)):(t[i]=t[i]||[]).push(n)}}function Ge(t,e,n,i){var o={},s=t===Ye;function r(a){var l;return o[a]=!0,C.each(t[a]||[],(function(t,a){var c=a(e,n,i);return"string"!=typeof c||s||o[c]?s?!(l=c):void 0:(e.dataTypes.unshift(c),r(c),!1)})),l}return r(e.dataTypes[0])||!o["*"]&&r("*")}function Ke(t,e){var n,i,o=C.ajaxSettings.flatOptions||{};for(n in e)void 0!==e[n]&&((o[n]?t:i||(i={}))[n]=e[n]);return i&&C.extend(!0,t,i),t}Ve.href=Oe.href,C.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Oe.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(Oe.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Ue,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":C.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(t,e){return e?Ke(Ke(t,C.ajaxSettings),e):Ke(C.ajaxSettings,t)},ajaxPrefilter:Ze(Xe),ajaxTransport:Ze(Ye),ajax:function(t,e){"object"==typeof t&&(e=t,t=void 0),e=e||{};var n,o,s,r,a,l,c,u,d,h,f=C.ajaxSetup({},e),p=f.context||f,g=f.context&&(p.nodeType||p.jquery)?C(p):C.event,m=C.Deferred(),v=C.Callbacks("once memory"),b=f.statusCode||{},x={},w={},E="canceled",S={readyState:0,getResponseHeader:function(t){var e;if(c){if(!r)for(r={};e=qe.exec(s);)r[e[1].toLowerCase()+" "]=(r[e[1].toLowerCase()+" "]||[]).concat(e[2]);e=r[t.toLowerCase()+" "]}return null==e?null:e.join(", ")},getAllResponseHeaders:function(){return c?s:null},setRequestHeader:function(t,e){return null==c&&(t=w[t.toLowerCase()]=w[t.toLowerCase()]||t,x[t]=e),this},overrideMimeType:function(t){return null==c&&(f.mimeType=t),this},statusCode:function(t){var e;if(t)if(c)S.always(t[S.status]);else for(e in t)b[e]=[b[e],t[e]];return this},abort:function(t){var e=t||E;return n&&n.abort(e),T(0,e),this}};if(m.promise(S),f.url=((t||f.url||Oe.href)+"").replace(We,Oe.protocol+"//"),f.type=e.method||e.type||f.method||f.type,f.dataTypes=(f.dataType||"*").toLowerCase().match(U)||[""],null==f.crossDomain){l=y.createElement("a");try{l.href=f.url,l.href=l.href,f.crossDomain=Ve.protocol+"//"+Ve.host!=l.protocol+"//"+l.host}catch(t){f.crossDomain=!0}}if(f.data&&f.processData&&"string"!=typeof f.data&&(f.data=C.param(f.data,f.traditional)),Ge(Xe,f,e,S),c)return S;for(d in(u=C.event&&f.global)&&0==C.active++&&C.event.trigger("ajaxStart"),f.type=f.type.toUpperCase(),f.hasContent=!$e.test(f.type),o=f.url.replace(He,""),f.hasContent?f.data&&f.processData&&0===(f.contentType||"").indexOf("application/x-www-form-urlencoded")&&(f.data=f.data.replace(Ne,"+")):(h=f.url.slice(o.length),f.data&&(f.processData||"string"==typeof f.data)&&(o+=(Ae.test(o)?"&":"?")+f.data,delete f.data),!1===f.cache&&(o=o.replace(Be,"$1"),h=(Ae.test(o)?"&":"?")+"_="+Me.guid+++h),f.url=o+h),f.ifModified&&(C.lastModified[o]&&S.setRequestHeader("If-Modified-Since",C.lastModified[o]),C.etag[o]&&S.setRequestHeader("If-None-Match",C.etag[o])),(f.data&&f.hasContent&&!1!==f.contentType||e.contentType)&&S.setRequestHeader("Content-Type",f.contentType),S.setRequestHeader("Accept",f.dataTypes[0]&&f.accepts[f.dataTypes[0]]?f.accepts[f.dataTypes[0]]+("*"!==f.dataTypes[0]?", "+Ue+"; q=0.01":""):f.accepts["*"]),f.headers)S.setRequestHeader(d,f.headers[d]);if(f.beforeSend&&(!1===f.beforeSend.call(p,S,f)||c))return S.abort();if(E="abort",v.add(f.complete),S.done(f.success),S.fail(f.error),n=Ge(Ye,f,e,S)){if(S.readyState=1,u&&g.trigger("ajaxSend",[S,f]),c)return S;f.async&&f.timeout>0&&(a=i.setTimeout((function(){S.abort("timeout")}),f.timeout));try{c=!1,n.send(x,T)}catch(t){if(c)throw t;T(-1,t)}}else T(-1,"No Transport");function T(t,e,r,l){var d,h,y,x,w,E=e;c||(c=!0,a&&i.clearTimeout(a),n=void 0,s=l||"",S.readyState=t>0?4:0,d=t>=200&&t<300||304===t,r&&(x=function(t,e,n){for(var i,o,s,r,a=t.contents,l=t.dataTypes;"*"===l[0];)l.shift(),void 0===i&&(i=t.mimeType||e.getResponseHeader("Content-Type"));if(i)for(o in a)if(a[o]&&a[o].test(i)){l.unshift(o);break}if(l[0]in n)s=l[0];else{for(o in n){if(!l[0]||t.converters[o+" "+l[0]]){s=o;break}r||(r=o)}s=s||r}if(s)return s!==l[0]&&l.unshift(s),n[s]}(f,S,r)),!d&&C.inArray("script",f.dataTypes)>-1&&C.inArray("json",f.dataTypes)<0&&(f.converters["text script"]=function(){}),x=function(t,e,n,i){var o,s,r,a,l,c={},u=t.dataTypes.slice();if(u[1])for(r in t.converters)c[r.toLowerCase()]=t.converters[r];for(s=u.shift();s;)if(t.responseFields[s]&&(n[t.responseFields[s]]=e),!l&&i&&t.dataFilter&&(e=t.dataFilter(e,t.dataType)),l=s,s=u.shift())if("*"===s)s=l;else if("*"!==l&&l!==s){if(!(r=c[l+" "+s]||c["* "+s]))for(o in c)if((a=o.split(" "))[1]===s&&(r=c[l+" "+a[0]]||c["* "+a[0]])){!0===r?r=c[o]:!0!==c[o]&&(s=a[0],u.unshift(a[1]));break}if(!0!==r)if(r&&t.throws)e=r(e);else try{e=r(e)}catch(t){return{state:"parsererror",error:r?t:"No conversion from "+l+" to "+s}}}return{state:"success",data:e}}(f,x,S,d),d?(f.ifModified&&((w=S.getResponseHeader("Last-Modified"))&&(C.lastModified[o]=w),(w=S.getResponseHeader("etag"))&&(C.etag[o]=w)),204===t||"HEAD"===f.type?E="nocontent":304===t?E="notmodified":(E=x.state,h=x.data,d=!(y=x.error))):(y=E,!t&&E||(E="error",t<0&&(t=0))),S.status=t,S.statusText=(e||E)+"",d?m.resolveWith(p,[h,E,S]):m.rejectWith(p,[S,E,y]),S.statusCode(b),b=void 0,u&&g.trigger(d?"ajaxSuccess":"ajaxError",[S,f,d?h:y]),v.fireWith(p,[S,E]),u&&(g.trigger("ajaxComplete",[S,f]),--C.active||C.event.trigger("ajaxStop")))}return S},getJSON:function(t,e,n){return C.get(t,e,n,"json")},getScript:function(t,e){return C.get(t,void 0,e,"script")}}),C.each(["get","post"],(function(t,e){C[e]=function(t,n,i,o){return v(n)&&(o=o||i,i=n,n=void 0),C.ajax(C.extend({url:t,type:e,dataType:o,data:n,success:i},C.isPlainObject(t)&&t))}})),C.ajaxPrefilter((function(t){var e;for(e in t.headers)"content-type"===e.toLowerCase()&&(t.contentType=t.headers[e]||"")})),C._evalUrl=function(t,e,n){return C.ajax({url:t,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(t){C.globalEval(t,e,n)}})},C.fn.extend({wrapAll:function(t){var e;return this[0]&&(v(t)&&(t=t.call(this[0])),e=C(t,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&e.insertBefore(this[0]),e.map((function(){for(var t=this;t.firstElementChild;)t=t.firstElementChild;return t})).append(this)),this},wrapInner:function(t){return v(t)?this.each((function(e){C(this).wrapInner(t.call(this,e))})):this.each((function(){var e=C(this),n=e.contents();n.length?n.wrapAll(t):e.append(t)}))},wrap:function(t){var e=v(t);return this.each((function(n){C(this).wrapAll(e?t.call(this,n):t)}))},unwrap:function(t){return this.parent(t).not("body").each((function(){C(this).replaceWith(this.childNodes)})),this}}),C.expr.pseudos.hidden=function(t){return!C.expr.pseudos.visible(t)},C.expr.pseudos.visible=function(t){return!!(t.offsetWidth||t.offsetHeight||t.getClientRects().length)},C.ajaxSettings.xhr=function(){try{return new i.XMLHttpRequest}catch(t){}};var Je={0:200,1223:204},Qe=C.ajaxSettings.xhr();m.cors=!!Qe&&"withCredentials"in Qe,m.ajax=Qe=!!Qe,C.ajaxTransport((function(t){var e,n;if(m.cors||Qe&&!t.crossDomain)return{send:function(o,s){var r,a=t.xhr();if(a.open(t.type,t.url,t.async,t.username,t.password),t.xhrFields)for(r in t.xhrFields)a[r]=t.xhrFields[r];for(r in t.mimeType&&a.overrideMimeType&&a.overrideMimeType(t.mimeType),t.crossDomain||o["X-Requested-With"]||(o["X-Requested-With"]="XMLHttpRequest"),o)a.setRequestHeader(r,o[r]);e=function(t){return function(){e&&(e=n=a.onload=a.onerror=a.onabort=a.ontimeout=a.onreadystatechange=null,"abort"===t?a.abort():"error"===t?"number"!=typeof a.status?s(0,"error"):s(a.status,a.statusText):s(Je[a.status]||a.status,a.statusText,"text"!==(a.responseType||"text")||"string"!=typeof a.responseText?{binary:a.response}:{text:a.responseText},a.getAllResponseHeaders()))}},a.onload=e(),n=a.onerror=a.ontimeout=e("error"),void 0!==a.onabort?a.onabort=n:a.onreadystatechange=function(){4===a.readyState&&i.setTimeout((function(){e&&n()}))},e=e("abort");try{a.send(t.hasContent&&t.data||null)}catch(t){if(e)throw t}},abort:function(){e&&e()}}})),C.ajaxPrefilter((function(t){t.crossDomain&&(t.contents.script=!1)})),C.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(t){return C.globalEval(t),t}}}),C.ajaxPrefilter("script",(function(t){void 0===t.cache&&(t.cache=!1),t.crossDomain&&(t.type="GET")})),C.ajaxTransport("script",(function(t){var e,n;if(t.crossDomain||t.scriptAttrs)return{send:function(i,o){e=C("<script>").attr(t.scriptAttrs||{}).prop({charset:t.scriptCharset,src:t.url}).on("load error",n=function(t){e.remove(),n=null,t&&o("error"===t.type?404:200,t.type)}),y.head.appendChild(e[0])},abort:function(){n&&n()}}}));var tn,en=[],nn=/(=)\?(?=&|$)|\?\?/;C.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var t=en.pop()||C.expando+"_"+Me.guid++;return this[t]=!0,t}}),C.ajaxPrefilter("json jsonp",(function(t,e,n){var o,s,r,a=!1!==t.jsonp&&(nn.test(t.url)?"url":"string"==typeof t.data&&0===(t.contentType||"").indexOf("application/x-www-form-urlencoded")&&nn.test(t.data)&&"data");if(a||"jsonp"===t.dataTypes[0])return o=t.jsonpCallback=v(t.jsonpCallback)?t.jsonpCallback():t.jsonpCallback,a?t[a]=t[a].replace(nn,"$1"+o):!1!==t.jsonp&&(t.url+=(Ae.test(t.url)?"&":"?")+t.jsonp+"="+o),t.converters["script json"]=function(){return r||C.error(o+" was not called"),r[0]},t.dataTypes[0]="json",s=i[o],i[o]=function(){r=arguments},n.always((function(){void 0===s?C(i).removeProp(o):i[o]=s,t[o]&&(t.jsonpCallback=e.jsonpCallback,en.push(o)),r&&v(s)&&s(r[0]),r=s=void 0})),"script"})),m.createHTMLDocument=((tn=y.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===tn.childNodes.length),C.parseHTML=function(t,e,n){return"string"!=typeof t?[]:("boolean"==typeof e&&(n=e,e=!1),e||(m.createHTMLDocument?((i=(e=y.implementation.createHTMLDocument("")).createElement("base")).href=y.location.href,e.head.appendChild(i)):e=y),s=!n&&[],(o=H.exec(t))?[e.createElement(o[1])]:(o=Lt([t],e,s),s&&s.length&&C(s).remove(),C.merge([],o.childNodes)));var i,o,s},C.fn.load=function(t,e,n){var i,o,s,r=this,a=t.indexOf(" ");return a>-1&&(i=Ce(t.slice(a)),t=t.slice(0,a)),v(e)?(n=e,e=void 0):e&&"object"==typeof e&&(o="POST"),r.length>0&&C.ajax({url:t,type:o||"GET",dataType:"html",data:e}).done((function(t){s=arguments,r.html(i?C("<div>").append(C.parseHTML(t)).find(i):t)})).always(n&&function(t,e){r.each((function(){n.apply(this,s||[t.responseText,e,t])}))}),this},C.expr.pseudos.animated=function(t){return C.grep(C.timers,(function(e){return t===e.elem})).length},C.offset={setOffset:function(t,e,n){var i,o,s,r,a,l,c=C.css(t,"position"),u=C(t),d={};"static"===c&&(t.style.position="relative"),a=u.offset(),s=C.css(t,"top"),l=C.css(t,"left"),("absolute"===c||"fixed"===c)&&(s+l).indexOf("auto")>-1?(r=(i=u.position()).top,o=i.left):(r=parseFloat(s)||0,o=parseFloat(l)||0),v(e)&&(e=e.call(t,n,C.extend({},a))),null!=e.top&&(d.top=e.top-a.top+r),null!=e.left&&(d.left=e.left-a.left+o),"using"in e?e.using.call(t,d):u.css(d)}},C.fn.extend({offset:function(t){if(arguments.length)return void 0===t?this:this.each((function(e){C.offset.setOffset(this,t,e)}));var e,n,i=this[0];return i?i.getClientRects().length?(e=i.getBoundingClientRect(),n=i.ownerDocument.defaultView,{top:e.top+n.pageYOffset,left:e.left+n.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var t,e,n,i=this[0],o={top:0,left:0};if("fixed"===C.css(i,"position"))e=i.getBoundingClientRect();else{for(e=this.offset(),n=i.ownerDocument,t=i.offsetParent||n.documentElement;t&&(t===n.body||t===n.documentElement)&&"static"===C.css(t,"position");)t=t.parentNode;t&&t!==i&&1===t.nodeType&&((o=C(t).offset()).top+=C.css(t,"borderTopWidth",!0),o.left+=C.css(t,"borderLeftWidth",!0))}return{top:e.top-o.top-C.css(i,"marginTop",!0),left:e.left-o.left-C.css(i,"marginLeft",!0)}}},offsetParent:function(){return this.map((function(){for(var t=this.offsetParent;t&&"static"===C.css(t,"position");)t=t.offsetParent;return t||gt}))}}),C.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},(function(t,e){var n="pageYOffset"===e;C.fn[t]=function(i){return tt(this,(function(t,i,o){var s;if(b(t)?s=t:9===t.nodeType&&(s=t.defaultView),void 0===o)return s?s[e]:t[i];s?s.scrollTo(n?s.pageXOffset:o,n?o:s.pageYOffset):t[i]=o}),t,i,arguments.length)}})),C.each(["top","left"],(function(t,e){C.cssHooks[e]=te(m.pixelPosition,(function(t,n){if(n)return n=Qt(t,e),Vt.test(n)?C(t).position()[e]+"px":n}))})),C.each({Height:"height",Width:"width"},(function(t,e){C.each({padding:"inner"+t,content:e,"":"outer"+t},(function(n,i){C.fn[i]=function(o,s){var r=arguments.length&&(n||"boolean"!=typeof o),a=n||(!0===o||!0===s?"margin":"border");return tt(this,(function(e,n,o){var s;return b(e)?0===i.indexOf("outer")?e["inner"+t]:e.document.documentElement["client"+t]:9===e.nodeType?(s=e.documentElement,Math.max(e.body["scroll"+t],s["scroll"+t],e.body["offset"+t],s["offset"+t],s["client"+t])):void 0===o?C.css(e,n,a):C.style(e,n,o,a)}),e,r?o:void 0,r)}}))})),C.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],(function(t,e){C.fn[e]=function(t){return this.on(e,t)}})),C.fn.extend({bind:function(t,e,n){return this.on(t,null,e,n)},unbind:function(t,e){return this.off(t,null,e)},delegate:function(t,e,n,i){return this.on(e,t,n,i)},undelegate:function(t,e,n){return 1===arguments.length?this.off(t,"**"):this.off(e,t||"**",n)},hover:function(t,e){return this.on("mouseenter",t).on("mouseleave",e||t)}}),C.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),(function(t,e){C.fn[e]=function(t,n){return arguments.length>0?this.on(e,null,t,n):this.trigger(e)}}));var on=/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g;C.proxy=function(t,e){var n,i,o;if("string"==typeof e&&(n=t[e],e=t,t=n),v(t))return i=a.call(arguments,2),o=function(){return t.apply(e||this,i.concat(a.call(arguments)))},o.guid=t.guid=t.guid||C.guid++,o},C.holdReady=function(t){t?C.readyWait++:C.ready(!0)},C.isArray=Array.isArray,C.parseJSON=JSON.parse,C.nodeName=P,C.isFunction=v,C.isWindow=b,C.camelCase=ot,C.type=E,C.now=Date.now,C.isNumeric=function(t){var e=C.type(t);return("number"===e||"string"===e)&&!isNaN(t-parseFloat(t))},C.trim=function(t){return null==t?"":(t+"").replace(on,"$1")},void 0===(n=function(){return C}.apply(e,[]))||(t.exports=n);var sn=i.jQuery,rn=i.$;return C.noConflict=function(t){return i.$===C&&(i.$=rn),t&&i.jQuery===C&&(i.jQuery=sn),C},void 0===o&&(i.jQuery=i.$=C),C}))},379:function(t){"use strict";var e=[];function n(t){for(var n=-1,i=0;i<e.length;i++)if(e[i].identifier===t){n=i;break}return n}function i(t,i){for(var s={},r=[],a=0;a<t.length;a++){var l=t[a],c=i.base?l[0]+i.base:l[0],u=s[c]||0,d="".concat(c," ").concat(u);s[c]=u+1;var h=n(d),f={css:l[1],media:l[2],sourceMap:l[3],supports:l[4],layer:l[5]};if(-1!==h)e[h].references++,e[h].updater(f);else{var p=o(f,i);i.byIndex=a,e.splice(a,0,{identifier:d,updater:p,references:1})}r.push(d)}return r}function o(t,e){var n=e.domAPI(e);return n.update(t),function(e){if(e){if(e.css===t.css&&e.media===t.media&&e.sourceMap===t.sourceMap&&e.supports===t.supports&&e.layer===t.layer)return;n.update(t=e)}else n.remove()}}t.exports=function(t,o){var s=i(t=t||[],o=o||{});return function(t){t=t||[];for(var r=0;r<s.length;r++){var a=n(s[r]);e[a].references--}for(var l=i(t,o),c=0;c<s.length;c++){var u=n(s[c]);0===e[u].references&&(e[u].updater(),e.splice(u,1))}s=l}}},569:function(t){"use strict";var e={};t.exports=function(t,n){var i=function(t){if(void 0===e[t]){var n=document.querySelector(t);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(t){n=null}e[t]=n}return e[t]}(t);if(!i)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");i.appendChild(n)}},216:function(t){"use strict";t.exports=function(t){var e=document.createElement("style");return t.setAttributes(e,t.attributes),t.insert(e,t.options),e}},565:function(t,e,n){"use strict";t.exports=function(t){var e=n.nc;e&&t.setAttribute("nonce",e)}},795:function(t){"use strict";t.exports=function(t){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var e=t.insertStyleElement(t);return{update:function(n){!function(t,e,n){var i="";n.supports&&(i+="@supports (".concat(n.supports,") {")),n.media&&(i+="@media ".concat(n.media," {"));var o=void 0!==n.layer;o&&(i+="@layer".concat(n.layer.length>0?" ".concat(n.layer):""," {")),i+=n.css,o&&(i+="}"),n.media&&(i+="}"),n.supports&&(i+="}");var s=n.sourceMap;s&&"undefined"!=typeof btoa&&(i+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(s))))," */")),e.styleTagTransform(i,t,e.options)}(e,t,n)},remove:function(){!function(t){if(null===t.parentNode)return!1;t.parentNode.removeChild(t)}(e)}}}},589:function(t){"use strict";t.exports=function(t,e){if(e.styleSheet)e.styleSheet.cssText=t;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(t))}}}},e={};function n(i){var o=e[i];if(void 0!==o)return o.exports;var s=e[i]={id:i,exports:{}};return t[i].call(s.exports,s,s.exports,n),s.exports}n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,{a:e}),e},n.d=function(t,e){for(var i in e)n.o(e,i)&&!n.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.nc=void 0,function(){"use strict";var t=n(755),e=n.n(t);const i=(t,e=1e4)=>(t=parseFloat(t+"")||0,Math.round((t+Number.EPSILON)*e)/e),o=function(t){if(!(t&&t instanceof Element&&t.offsetParent))return!1;const e=t.scrollHeight>t.clientHeight,n=window.getComputedStyle(t).overflowY,i=-1!==n.indexOf("hidden"),o=-1!==n.indexOf("visible");return e&&!i&&!o},s=function(t,e=void 0){return!(!t||t===document.body||e&&t===e)&&(o(t)?t:s(t.parentElement,e))},r=function(t){var e=(new DOMParser).parseFromString(t,"text/html").body;if(e.childElementCount>1){for(var n=document.createElement("div");e.firstChild;)n.appendChild(e.firstChild);return n}return e.firstChild},a=t=>`${t||""}`.split(" ").filter((t=>!!t)),l=(t,e,n)=>{t&&a(e).forEach((e=>{t.classList.toggle(e,n||!1)}))};class c{constructor(t){Object.defineProperty(this,"pageX",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"pageY",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"clientX",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"clientY",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"id",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"time",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"nativePointer",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.nativePointer=t,this.pageX=t.pageX,this.pageY=t.pageY,this.clientX=t.clientX,this.clientY=t.clientY,this.id=self.Touch&&t instanceof Touch?t.identifier:-1,this.time=Date.now()}}const u={passive:!1};class d{constructor(t,{start:e=(()=>!0),move:n=(()=>{}),end:i=(()=>{})}){Object.defineProperty(this,"element",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"startCallback",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"moveCallback",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"endCallback",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"currentPointers",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"startPointers",{enumerable:!0,configurable:!0,writable:!0,value:[]}),this.element=t,this.startCallback=e,this.moveCallback=n,this.endCallback=i;for(const t of["onPointerStart","onTouchStart","onMove","onTouchEnd","onPointerEnd","onWindowBlur"])this[t]=this[t].bind(this);this.element.addEventListener("mousedown",this.onPointerStart,u),this.element.addEventListener("touchstart",this.onTouchStart,u),this.element.addEventListener("touchmove",this.onMove,u),this.element.addEventListener("touchend",this.onTouchEnd),this.element.addEventListener("touchcancel",this.onTouchEnd)}onPointerStart(t){if(!t.buttons||0!==t.button)return;const e=new c(t);this.currentPointers.some((t=>t.id===e.id))||this.triggerPointerStart(e,t)&&(window.addEventListener("mousemove",this.onMove),window.addEventListener("mouseup",this.onPointerEnd),window.addEventListener("blur",this.onWindowBlur))}onTouchStart(t){for(const e of Array.from(t.changedTouches||[]))this.triggerPointerStart(new c(e),t);window.addEventListener("blur",this.onWindowBlur)}onMove(t){const e=this.currentPointers.slice(),n="changedTouches"in t?Array.from(t.changedTouches||[]).map((t=>new c(t))):[new c(t)],i=[];for(const t of n){const e=this.currentPointers.findIndex((e=>e.id===t.id));e<0||(i.push(t),this.currentPointers[e]=t)}i.length&&this.moveCallback(t,this.currentPointers.slice(),e)}onPointerEnd(t){t.buttons>0&&0!==t.button||(this.triggerPointerEnd(t,new c(t)),window.removeEventListener("mousemove",this.onMove),window.removeEventListener("mouseup",this.onPointerEnd),window.removeEventListener("blur",this.onWindowBlur))}onTouchEnd(t){for(const e of Array.from(t.changedTouches||[]))this.triggerPointerEnd(t,new c(e))}triggerPointerStart(t,e){return!!this.startCallback(e,t,this.currentPointers.slice())&&(this.currentPointers.push(t),this.startPointers.push(t),!0)}triggerPointerEnd(t,e){const n=this.currentPointers.findIndex((t=>t.id===e.id));n<0||(this.currentPointers.splice(n,1),this.startPointers.splice(n,1),this.endCallback(t,e,this.currentPointers.slice()))}onWindowBlur(){this.clear()}clear(){for(;this.currentPointers.length;){const t=this.currentPointers[this.currentPointers.length-1];this.currentPointers.splice(this.currentPointers.length-1,1),this.startPointers.splice(this.currentPointers.length-1,1),this.endCallback(new Event("touchend",{bubbles:!0,cancelable:!0,clientX:t.clientX,clientY:t.clientY}),t,this.currentPointers.slice())}}stop(){this.element.removeEventListener("mousedown",this.onPointerStart,u),this.element.removeEventListener("touchstart",this.onTouchStart,u),this.element.removeEventListener("touchmove",this.onMove,u),this.element.removeEventListener("touchend",this.onTouchEnd),this.element.removeEventListener("touchcancel",this.onTouchEnd),window.removeEventListener("mousemove",this.onMove),window.removeEventListener("mouseup",this.onPointerEnd),window.removeEventListener("blur",this.onWindowBlur)}}function h(t,e){return e?Math.sqrt(Math.pow(e.clientX-t.clientX,2)+Math.pow(e.clientY-t.clientY,2)):0}function f(t,e){return e?{clientX:(t.clientX+e.clientX)/2,clientY:(t.clientY+e.clientY)/2}:t}const p=t=>"object"==typeof t&&null!==t&&t.constructor===Object&&"[object Object]"===Object.prototype.toString.call(t),g=(t,...e)=>{const n=e.length;for(let i=0;i<n;i++){const n=e[i]||{};Object.entries(n).forEach((([e,n])=>{const i=Array.isArray(n)?[]:{};t[e]||Object.assign(t,{[e]:i}),p(n)?Object.assign(t[e],g(i,n)):Array.isArray(n)?Object.assign(t,{[e]:[...n]}):Object.assign(t,{[e]:n})}))}return t},m=function(t,e){return t.split(".").reduce(((t,e)=>"object"==typeof t?t[e]:void 0),e)};class v{constructor(t={}){Object.defineProperty(this,"options",{enumerable:!0,configurable:!0,writable:!0,value:t}),Object.defineProperty(this,"events",{enumerable:!0,configurable:!0,writable:!0,value:new Map}),this.setOptions(t);for(const t of Object.getOwnPropertyNames(Object.getPrototypeOf(this)))t.startsWith("on")&&"function"==typeof this[t]&&(this[t]=this[t].bind(this))}setOptions(t){this.options=t?g({},this.constructor.defaults,t):{};for(const[t,e]of Object.entries(this.option("on")||{}))this.on(t,e)}option(t,...e){let n=m(t,this.options);return n&&"function"==typeof n&&(n=n.call(this,this,...e)),n}optionFor(t,e,n,...i){let o=m(e,t);var s;"string"!=typeof(s=o)||isNaN(s)||isNaN(parseFloat(s))||(o=parseFloat(o)),"true"===o&&(o=!0),"false"===o&&(o=!1),o&&"function"==typeof o&&(o=o.call(this,this,t,...i));let r=m(e,this.options);return r&&"function"==typeof r?o=r.call(this,this,t,...i,o):void 0===o&&(o=r),void 0===o?n:o}cn(t){const e=this.options.classes;return e&&e[t]||""}localize(t,e=[]){t=String(t).replace(/\{\{(\w+).?(\w+)?\}\}/g,((t,e,n)=>{let i="";return n?i=this.option(`${e[0]+e.toLowerCase().substring(1)}.l10n.${n}`):e&&(i=this.option(`l10n.${e}`)),i||(i=t),i}));for(let n=0;n<e.length;n++)t=t.split(e[n][0]).join(e[n][1]);return t.replace(/\{\{(.*?)\}\}/g,((t,e)=>e))}on(t,e){let n=[];"string"==typeof t?n=t.split(" "):Array.isArray(t)&&(n=t),this.events||(this.events=new Map),n.forEach((t=>{let n=this.events.get(t);n||(this.events.set(t,[]),n=[]),n.includes(e)||n.push(e),this.events.set(t,n)}))}off(t,e){let n=[];"string"==typeof t?n=t.split(" "):Array.isArray(t)&&(n=t),n.forEach((t=>{const n=this.events.get(t);if(Array.isArray(n)){const t=n.indexOf(e);t>-1&&n.splice(t,1)}}))}emit(t,...e){[...this.events.get(t)||[]].forEach((t=>t(this,...e))),"*"!==t&&this.emit("*",t,...e)}}Object.defineProperty(v,"version",{enumerable:!0,configurable:!0,writable:!0,value:"5.0.35"}),Object.defineProperty(v,"defaults",{enumerable:!0,configurable:!0,writable:!0,value:{}});class b extends v{constructor(t={}){super(t),Object.defineProperty(this,"plugins",{enumerable:!0,configurable:!0,writable:!0,value:{}})}attachPlugins(t={}){const e=new Map;for(const[n,i]of Object.entries(t)){const t=this.option(n),o=this.plugins[n];o||!1===t?o&&!1===t&&(o.detach(),delete this.plugins[n]):e.set(n,new i(this,t||{}))}for(const[t,n]of e)this.plugins[t]=n,n.attach()}detachPlugins(t){t=t||Object.keys(this.plugins);for(const e of t){const t=this.plugins[e];t&&t.detach(),delete this.plugins[e]}return this.emit("detachPlugins"),this}}var y;!function(t){t[t.Init=0]="Init",t[t.Error=1]="Error",t[t.Ready=2]="Ready",t[t.Panning=3]="Panning",t[t.Mousemove=4]="Mousemove",t[t.Destroy=5]="Destroy"}(y||(y={}));const x=["a","b","c","d","e","f"],w={PANUP:"Move up",PANDOWN:"Move down",PANLEFT:"Move left",PANRIGHT:"Move right",ZOOMIN:"Zoom in",ZOOMOUT:"Zoom out",TOGGLEZOOM:"Toggle zoom level",TOGGLE1TO1:"Toggle zoom level",ITERATEZOOM:"Toggle zoom level",ROTATECCW:"Rotate counterclockwise",ROTATECW:"Rotate clockwise",FLIPX:"Flip horizontally",FLIPY:"Flip vertically",FITX:"Fit horizontally",FITY:"Fit vertically",RESET:"Reset",TOGGLEFS:"Toggle fullscreen"},E={content:null,width:"auto",height:"auto",panMode:"drag",touch:!0,dragMinThreshold:3,lockAxis:!1,mouseMoveFactor:1,mouseMoveFriction:.12,zoom:!0,pinchToZoom:!0,panOnlyZoomed:"auto",minScale:1,maxScale:2,friction:.25,dragFriction:.35,decelFriction:.05,click:"toggleZoom",dblClick:!1,wheel:"zoom",wheelLimit:7,spinner:!0,bounds:"auto",infinite:!1,rubberband:!0,bounce:!0,maxVelocity:75,transformParent:!1,classes:{content:"f-panzoom__content",isLoading:"is-loading",canZoomIn:"can-zoom_in",canZoomOut:"can-zoom_out",isDraggable:"is-draggable",isDragging:"is-dragging",inFullscreen:"in-fullscreen",htmlHasFullscreen:"with-panzoom-in-fullscreen"},l10n:w},S='<circle cx="25" cy="25" r="20"></circle>',T='<div class="f-spinner"><svg viewBox="0 0 50 50">'+S+S+"</svg></div>",C=t=>t&&null!==t&&t instanceof Element&&"nodeType"in t,_=(t,e)=>{t&&a(e).forEach((e=>{t.classList.remove(e)}))},P=(t,e)=>{t&&a(e).forEach((e=>{t.classList.add(e)}))},k={a:1,b:0,c:0,d:1,e:0,f:0},O=1e4,M="mousemove",A="drag",L="content",z="auto";let D=null,j=null;class I extends b{get fits(){return this.contentRect.width-this.contentRect.fitWidth<1&&this.contentRect.height-this.contentRect.fitHeight<1}get isTouchDevice(){return null===j&&(j=window.matchMedia("(hover: none)").matches),j}get isMobile(){return null===D&&(D=/iPhone|iPad|iPod|Android/i.test(navigator.userAgent)),D}get panMode(){return this.options.panMode!==M||this.isTouchDevice?A:M}get panOnlyZoomed(){const t=this.options.panOnlyZoomed;return t===z?this.isTouchDevice:t}get isInfinite(){return this.option("infinite")}get angle(){return 180*Math.atan2(this.current.b,this.current.a)/Math.PI||0}get targetAngle(){return 180*Math.atan2(this.target.b,this.target.a)/Math.PI||0}get scale(){const{a:t,b:e}=this.current;return Math.sqrt(t*t+e*e)||1}get targetScale(){const{a:t,b:e}=this.target;return Math.sqrt(t*t+e*e)||1}get minScale(){return this.option("minScale")||1}get fullScale(){const{contentRect:t}=this;return t.fullWidth/t.fitWidth||1}get maxScale(){return this.fullScale*(this.option("maxScale")||1)||1}get coverScale(){const{containerRect:t,contentRect:e}=this,n=Math.max(t.height/e.fitHeight,t.width/e.fitWidth)||1;return Math.min(this.fullScale,n)}get isScaling(){return Math.abs(this.targetScale-this.scale)>1e-5&&!this.isResting}get isContentLoading(){const t=this.content;return!!(t&&t instanceof HTMLImageElement)&&!t.complete}get isResting(){if(this.isBouncingX||this.isBouncingY)return!1;for(const t of x){const e="e"==t||"f"===t?1e-4:1e-5;if(Math.abs(this.target[t]-this.current[t])>e)return!1}return!(!this.ignoreBounds&&!this.checkBounds().inBounds)}constructor(t,e={},n={}){var i;if(super(e),Object.defineProperty(this,"pointerTracker",{enumerable:!0,configurable:!0,writable:!0,value:null}),Object.defineProperty(this,"resizeObserver",{enumerable:!0,configurable:!0,writable:!0,value:null}),Object.defineProperty(this,"updateTimer",{enumerable:!0,configurable:!0,writable:!0,value:null}),Object.defineProperty(this,"clickTimer",{enumerable:!0,configurable:!0,writable:!0,value:null}),Object.defineProperty(this,"rAF",{enumerable:!0,configurable:!0,writable:!0,value:null}),Object.defineProperty(this,"isTicking",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"ignoreBounds",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"isBouncingX",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"isBouncingY",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"clicks",{enumerable:!0,configurable:!0,writable:!0,value:0}),Object.defineProperty(this,"trackingPoints",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"pwt",{enumerable:!0,configurable:!0,writable:!0,value:0}),Object.defineProperty(this,"cwd",{enumerable:!0,configurable:!0,writable:!0,value:0}),Object.defineProperty(this,"pmme",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"friction",{enumerable:!0,configurable:!0,writable:!0,value:0}),Object.defineProperty(this,"state",{enumerable:!0,configurable:!0,writable:!0,value:y.Init}),Object.defineProperty(this,"isDragging",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"container",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"content",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"spinner",{enumerable:!0,configurable:!0,writable:!0,value:null}),Object.defineProperty(this,"containerRect",{enumerable:!0,configurable:!0,writable:!0,value:{width:0,height:0,innerWidth:0,innerHeight:0}}),Object.defineProperty(this,"contentRect",{enumerable:!0,configurable:!0,writable:!0,value:{top:0,right:0,bottom:0,left:0,fullWidth:0,fullHeight:0,fitWidth:0,fitHeight:0,width:0,height:0}}),Object.defineProperty(this,"dragStart",{enumerable:!0,configurable:!0,writable:!0,value:{x:0,y:0,top:0,left:0,time:0}}),Object.defineProperty(this,"dragOffset",{enumerable:!0,configurable:!0,writable:!0,value:{x:0,y:0,time:0}}),Object.defineProperty(this,"current",{enumerable:!0,configurable:!0,writable:!0,value:Object.assign({},k)}),Object.defineProperty(this,"target",{enumerable:!0,configurable:!0,writable:!0,value:Object.assign({},k)}),Object.defineProperty(this,"velocity",{enumerable:!0,configurable:!0,writable:!0,value:{a:0,b:0,c:0,d:0,e:0,f:0}}),Object.defineProperty(this,"lockedAxis",{enumerable:!0,configurable:!0,writable:!0,value:!1}),!t)throw new Error("Container Element Not Found");this.container=t,this.initContent(),this.attachPlugins(Object.assign(Object.assign({},I.Plugins),n)),this.emit("attachPlugins"),this.emit("init");const o=this.content;if(o.addEventListener("load",this.onLoad),o.addEventListener("error",this.onError),this.isContentLoading){if(this.option("spinner")){t.classList.add(this.cn("isLoading"));const e=r(T);!t.contains(o)||o.parentElement instanceof HTMLPictureElement?this.spinner=t.appendChild(e):this.spinner=(null===(i=o.parentElement)||void 0===i?void 0:i.insertBefore(e,o))||null}this.emit("beforeLoad")}else queueMicrotask((()=>{this.enable()}))}initContent(){const{container:t}=this,e=this.cn(L);let n=this.option(L)||t.querySelector(`.${e}`);if(n||(n=t.querySelector("img,picture")||t.firstElementChild,n&&P(n,e)),n instanceof HTMLPictureElement&&(n=n.querySelector("img")),!n)throw new Error("No content found");this.content=n}onLoad(){const{spinner:t,container:e,state:n}=this;t&&(t.remove(),this.spinner=null),this.option("spinner")&&e.classList.remove(this.cn("isLoading")),this.emit("afterLoad"),n===y.Init?this.enable():this.updateMetrics()}onError(){this.state!==y.Destroy&&(this.spinner&&(this.spinner.remove(),this.spinner=null),this.stop(),this.detachEvents(),this.state=y.Error,this.emit("error"))}getNextScale(t){const{fullScale:e,targetScale:n,coverScale:i,maxScale:o,minScale:s}=this;let r=s;switch(t){case"toggleMax":r=n-s<.5*(o-s)?o:s;break;case"toggleCover":r=n-s<.5*(i-s)?i:s;break;case"toggleZoom":r=n-s<.5*(e-s)?e:s;break;case"iterateZoom":let t=[1,e,o].sort(((t,e)=>t-e)),a=t.findIndex((t=>t>n+1e-5));r=t[a]||1}return r}attachObserver(){var t;const e=()=>{const{container:t,containerRect:e}=this;return Math.abs(e.width-t.getBoundingClientRect().width)>.1||Math.abs(e.height-t.getBoundingClientRect().height)>.1};this.resizeObserver||void 0===window.ResizeObserver||(this.resizeObserver=new ResizeObserver((()=>{this.updateTimer||(e()?(this.onResize(),this.isMobile&&(this.updateTimer=setTimeout((()=>{e()&&this.onResize(),this.updateTimer=null}),500))):this.updateTimer&&(clearTimeout(this.updateTimer),this.updateTimer=null))}))),null===(t=this.resizeObserver)||void 0===t||t.observe(this.container)}detachObserver(){var t;null===(t=this.resizeObserver)||void 0===t||t.disconnect()}attachEvents(){const{container:t}=this;t.addEventListener("click",this.onClick,{passive:!1,capture:!1}),t.addEventListener("wheel",this.onWheel,{passive:!1}),this.pointerTracker=new d(t,{start:this.onPointerDown,move:this.onPointerMove,end:this.onPointerUp}),document.addEventListener(M,this.onMouseMove)}detachEvents(){var t;const{container:e}=this;e.removeEventListener("click",this.onClick,{passive:!1,capture:!1}),e.removeEventListener("wheel",this.onWheel,{passive:!1}),null===(t=this.pointerTracker)||void 0===t||t.stop(),this.pointerTracker=null,document.removeEventListener(M,this.onMouseMove),document.removeEventListener("keydown",this.onKeydown,!0),this.clickTimer&&(clearTimeout(this.clickTimer),this.clickTimer=null),this.updateTimer&&(clearTimeout(this.updateTimer),this.updateTimer=null)}animate(){this.setTargetForce();const t=this.friction,e=this.option("maxVelocity");for(const n of x)t?(this.velocity[n]*=1-t,e&&!this.isScaling&&(this.velocity[n]=Math.max(Math.min(this.velocity[n],e),-1*e)),this.current[n]+=this.velocity[n]):this.current[n]=this.target[n];this.setTransform(),this.setEdgeForce(),!this.isResting||this.isDragging?this.rAF=requestAnimationFrame((()=>this.animate())):this.stop("current")}setTargetForce(){for(const t of x)"e"===t&&this.isBouncingX||"f"===t&&this.isBouncingY||(this.velocity[t]=(1/(1-this.friction)-1)*(this.target[t]-this.current[t]))}checkBounds(t=0,e=0){const{current:n}=this,i=n.e+t,o=n.f+e,s=this.getBounds(),{x:r,y:a}=s,l=r.min,c=r.max,u=a.min,d=a.max;let h=0,f=0;return l!==1/0&&i<l?h=l-i:c!==1/0&&i>c&&(h=c-i),u!==1/0&&o<u?f=u-o:d!==1/0&&o>d&&(f=d-o),Math.abs(h)<1e-4&&(h=0),Math.abs(f)<1e-4&&(f=0),Object.assign(Object.assign({},s),{xDiff:h,yDiff:f,inBounds:!h&&!f})}clampTargetBounds(){const{target:t}=this,{x:e,y:n}=this.getBounds();e.min!==1/0&&(t.e=Math.max(t.e,e.min)),e.max!==1/0&&(t.e=Math.min(t.e,e.max)),n.min!==1/0&&(t.f=Math.max(t.f,n.min)),n.max!==1/0&&(t.f=Math.min(t.f,n.max))}calculateContentDim(t=this.current){const{content:e,contentRect:n}=this,{fitWidth:i,fitHeight:o,fullWidth:s,fullHeight:r}=n;let a=s,l=r;if(this.option("zoom")||0!==this.angle){const n=!(e instanceof HTMLImageElement||"none"!==window.getComputedStyle(e).maxWidth&&"none"!==window.getComputedStyle(e).maxHeight),c=n?s:i,u=n?r:o,d=this.getMatrix(t),h=new DOMPoint(0,0).matrixTransform(d),f=new DOMPoint(0+c,0).matrixTransform(d),p=new DOMPoint(0+c,0+u).matrixTransform(d),g=new DOMPoint(0,0+u).matrixTransform(d),m=Math.abs(p.x-h.x),v=Math.abs(p.y-h.y),b=Math.abs(g.x-f.x),y=Math.abs(g.y-f.y);a=Math.max(m,b),l=Math.max(v,y)}return{contentWidth:a,contentHeight:l}}setEdgeForce(){if(this.ignoreBounds||this.isDragging||this.panMode===M||this.targetScale<this.scale)return this.isBouncingX=!1,void(this.isBouncingY=!1);const{target:t}=this,{x:e,y:n,xDiff:i,yDiff:o}=this.checkBounds(),s=this.option("maxVelocity");let r=this.velocity.e,a=this.velocity.f;0!==i?(this.isBouncingX=!0,i*r<=0?r+=.14*i:(r=.14*i,e.min!==1/0&&(this.target.e=Math.max(t.e,e.min)),e.max!==1/0&&(this.target.e=Math.min(t.e,e.max))),s&&(r=Math.max(Math.min(r,s),-1*s))):this.isBouncingX=!1,0!==o?(this.isBouncingY=!0,o*a<=0?a+=.14*o:(a=.14*o,n.min!==1/0&&(this.target.f=Math.max(t.f,n.min)),n.max!==1/0&&(this.target.f=Math.min(t.f,n.max))),s&&(a=Math.max(Math.min(a,s),-1*s))):this.isBouncingY=!1,this.isBouncingX&&(this.velocity.e=r),this.isBouncingY&&(this.velocity.f=a)}enable(){const{content:t}=this,e=new DOMMatrixReadOnly(window.getComputedStyle(t).transform);for(const t of x)this.current[t]=this.target[t]=e[t];this.updateMetrics(),this.attachObserver(),this.attachEvents(),this.state=y.Ready,this.emit("ready")}onClick(t){var e;"click"===t.type&&0===t.detail&&(this.dragOffset.x=0,this.dragOffset.y=0),this.isDragging&&(null===(e=this.pointerTracker)||void 0===e||e.clear(),this.trackingPoints=[],this.startDecelAnim());const n=t.target;if(!n||t.defaultPrevented)return;if(n.hasAttribute("disabled"))return t.preventDefault(),void t.stopPropagation();if((()=>{const t=window.getSelection();return t&&"Range"===t.type})()&&!n.closest("button"))return;const i=n.closest("[data-panzoom-action]"),o=n.closest("[data-panzoom-change]"),s=i||o,r=s&&C(s)?s.dataset:null;if(r){const e=r.panzoomChange,n=r.panzoomAction;if((e||n)&&t.preventDefault(),e){let n={};try{n=JSON.parse(e)}catch(t){console&&console.warn("The given data was not valid JSON")}return void this.applyChange(n)}if(n)return void(this[n]&&this[n]())}if(Math.abs(this.dragOffset.x)>3||Math.abs(this.dragOffset.y)>3)return t.preventDefault(),void t.stopPropagation();if(n.closest("[data-fancybox]"))return;const a=this.content.getBoundingClientRect(),l=this.dragStart;if(l.time&&!this.canZoomOut()&&(Math.abs(a.x-l.x)>2||Math.abs(a.y-l.y)>2))return;this.dragStart.time=0;const c=e=>{this.option("zoom",t)&&e&&"string"==typeof e&&/(iterateZoom)|(toggle(Zoom|Full|Cover|Max)|(zoomTo(Fit|Cover|Max)))/.test(e)&&"function"==typeof this[e]&&(t.preventDefault(),this[e]({event:t}))},u=this.option("click",t),d=this.option("dblClick",t);d?(this.clicks++,1==this.clicks&&(this.clickTimer=setTimeout((()=>{1===this.clicks?(this.emit("click",t),!t.defaultPrevented&&u&&c(u)):(this.emit("dblClick",t),t.defaultPrevented||c(d)),this.clicks=0,this.clickTimer=null}),350))):(this.emit("click",t),!t.defaultPrevented&&u&&c(u))}addTrackingPoint(t){const e=this.trackingPoints.filter((t=>t.time>Date.now()-100));e.push(t),this.trackingPoints=e}onPointerDown(t,e,n){var i;if(!1===this.option("touch",t))return!1;this.pwt=0,this.dragOffset={x:0,y:0,time:0},this.trackingPoints=[];const o=this.content.getBoundingClientRect();if(this.dragStart={x:o.x,y:o.y,top:o.top,left:o.left,time:Date.now()},this.clickTimer)return!1;if(this.panMode===M&&this.targetScale>1)return t.preventDefault(),t.stopPropagation(),!1;const s=t.composedPath()[0];if(!n.length){if(["TEXTAREA","OPTION","INPUT","SELECT","VIDEO","IFRAME"].includes(s.nodeName)||s.closest("[contenteditable],[data-selectable],[data-draggable],[data-clickable],[data-panzoom-change],[data-panzoom-action]"))return!1;null===(i=window.getSelection())||void 0===i||i.removeAllRanges()}if("mousedown"===t.type)["A","BUTTON"].includes(s.nodeName)||t.preventDefault();else if(Math.abs(this.velocity.a)>.3)return!1;return this.target.e=this.current.e,this.target.f=this.current.f,this.stop(),this.isDragging||(this.isDragging=!0,this.addTrackingPoint(e),this.emit("touchStart",t)),!0}onPointerMove(t,e,n){if(!1===this.option("touch",t))return;if(!this.isDragging)return;if(e.length<2&&this.panOnlyZoomed&&i(this.targetScale)<=i(this.minScale))return;if(this.emit("touchMove",t),t.defaultPrevented)return;this.addTrackingPoint(e[0]);const{content:o}=this,r=f(n[0],n[1]),a=f(e[0],e[1]);let l=0,c=0;if(e.length>1){const t=o.getBoundingClientRect();l=r.clientX-t.left-.5*t.width,c=r.clientY-t.top-.5*t.height}const u=h(n[0],n[1]),d=h(e[0],e[1]);let p=u?d/u:1,g=a.clientX-r.clientX,m=a.clientY-r.clientY;this.dragOffset.x+=g,this.dragOffset.y+=m,this.dragOffset.time=Date.now()-this.dragStart.time;let v=i(this.targetScale)===i(this.minScale)&&this.option("lockAxis");if(v&&!this.lockedAxis)if("xy"===v||"y"===v||"touchmove"===t.type){if(Math.abs(this.dragOffset.x)<6&&Math.abs(this.dragOffset.y)<6)return void t.preventDefault();const e=Math.abs(180*Math.atan2(this.dragOffset.y,this.dragOffset.x)/Math.PI);this.lockedAxis=e>45&&e<135?"y":"x",this.dragOffset.x=0,this.dragOffset.y=0,g=0,m=0}else this.lockedAxis=v;if(s(t.target,this.content)&&(v="x",this.dragOffset.y=0),v&&"xy"!==v&&this.lockedAxis!==v&&i(this.targetScale)===i(this.minScale))return;t.cancelable&&t.preventDefault(),this.container.classList.add(this.cn("isDragging"));const b=this.checkBounds(g,m);this.option("rubberband")?("x"!==this.isInfinite&&(b.xDiff>0&&g<0||b.xDiff<0&&g>0)&&(g*=Math.max(0,.5-Math.abs(.75/this.contentRect.fitWidth*b.xDiff))),"y"!==this.isInfinite&&(b.yDiff>0&&m<0||b.yDiff<0&&m>0)&&(m*=Math.max(0,.5-Math.abs(.75/this.contentRect.fitHeight*b.yDiff)))):(b.xDiff&&(g=0),b.yDiff&&(m=0));const y=this.targetScale,x=this.minScale,w=this.maxScale;y<.5*x&&(p=Math.max(p,x)),y>1.5*w&&(p=Math.min(p,w)),"y"===this.lockedAxis&&i(y)===i(x)&&(g=0),"x"===this.lockedAxis&&i(y)===i(x)&&(m=0),this.applyChange({originX:l,originY:c,panX:g,panY:m,scale:p,friction:this.option("dragFriction"),ignoreBounds:!0})}onPointerUp(t,e,n){if(n.length)return this.dragOffset.x=0,this.dragOffset.y=0,void(this.trackingPoints=[]);this.container.classList.remove(this.cn("isDragging")),this.isDragging&&(this.addTrackingPoint(e),this.panOnlyZoomed&&this.contentRect.width-this.contentRect.fitWidth<1&&this.contentRect.height-this.contentRect.fitHeight<1&&(this.trackingPoints=[]),s(t.target,this.content)&&"y"===this.lockedAxis&&(this.trackingPoints=[]),this.emit("touchEnd",t),this.isDragging=!1,this.lockedAxis=!1,this.state!==y.Destroy&&(t.defaultPrevented||this.startDecelAnim()))}startDecelAnim(){var t;const e=this.isScaling;this.rAF&&(cancelAnimationFrame(this.rAF),this.rAF=null),this.isBouncingX=!1,this.isBouncingY=!1;for(const t of x)this.velocity[t]=0;this.target.e=this.current.e,this.target.f=this.current.f,_(this.container,"is-scaling"),_(this.container,"is-animating"),this.isTicking=!1;const{trackingPoints:n}=this,o=n[0],s=n[n.length-1];let r=0,a=0,l=0;s&&o&&(r=s.clientX-o.clientX,a=s.clientY-o.clientY,l=s.time-o.time);const c=(null===(t=window.visualViewport)||void 0===t?void 0:t.scale)||1;1!==c&&(r*=c,a*=c);let u=0,d=0,h=0,f=0,p=this.option("decelFriction");const g=this.targetScale;if(l>0){h=Math.abs(r)>3?r/(l/30):0,f=Math.abs(a)>3?a/(l/30):0;const t=this.option("maxVelocity");t&&(h=Math.max(Math.min(h,t),-1*t),f=Math.max(Math.min(f,t),-1*t))}h&&(u=h/(1/(1-p)-1)),f&&(d=f/(1/(1-p)-1)),("y"===this.option("lockAxis")||"xy"===this.option("lockAxis")&&"y"===this.lockedAxis&&i(g)===this.minScale)&&(u=h=0),("x"===this.option("lockAxis")||"xy"===this.option("lockAxis")&&"x"===this.lockedAxis&&i(g)===this.minScale)&&(d=f=0);const m=this.dragOffset.x,v=this.dragOffset.y,b=this.option("dragMinThreshold")||0;Math.abs(m)<b&&Math.abs(v)<b&&(u=d=0,h=f=0),(this.option("zoom")&&(g<this.minScale-1e-5||g>this.maxScale+1e-5)||e&&!u&&!d)&&(p=.35),this.applyChange({panX:u,panY:d,friction:p}),this.emit("decel",h,f,m,v)}onWheel(t){var e=[-t.deltaX||0,-t.deltaY||0,-t.detail||0].reduce((function(t,e){return Math.abs(e)>Math.abs(t)?e:t}));const n=Math.max(-1,Math.min(1,e));if(this.emit("wheel",t,n),this.panMode===M)return;if(t.defaultPrevented)return;const i=this.option("wheel");"pan"===i?(t.preventDefault(),this.panOnlyZoomed&&!this.canZoomOut()||this.applyChange({panX:2*-t.deltaX,panY:2*-t.deltaY,bounce:!1})):"zoom"===i&&!1!==this.option("zoom")&&this.zoomWithWheel(t)}onMouseMove(t){this.panWithMouse(t)}onKeydown(t){"Escape"===t.key&&this.toggleFS()}onResize(){this.updateMetrics(),this.checkBounds().inBounds||this.requestTick()}setTransform(){this.emit("beforeTransform");const{current:t,target:e,content:n,contentRect:o}=this,s=Object.assign({},k);for(const n of x){const o="e"==n||"f"===n?O:1e5;s[n]=i(t[n],o),Math.abs(e[n]-t[n])<("e"==n||"f"===n?.51:.001)&&(t[n]=e[n])}let{a:r,b:a,c:l,d:c,e:u,f:d}=s,h=`matrix(${r}, ${a}, ${l}, ${c}, ${u}, ${d})`,f=n.parentElement instanceof HTMLPictureElement?n.parentElement:n;if(this.option("transformParent")&&(f=f.parentElement||f),f.style.transform===h)return;f.style.transform=h;const{contentWidth:p,contentHeight:g}=this.calculateContentDim();o.width=p,o.height=g,this.emit("afterTransform")}updateMetrics(t=!1){var e;if(!this||this.state===y.Destroy)return;if(this.isContentLoading)return;const n=Math.max(1,(null===(e=window.visualViewport)||void 0===e?void 0:e.scale)||1),{container:o,content:s}=this,r=s instanceof HTMLImageElement,a=o.getBoundingClientRect(),l=getComputedStyle(this.container);let c=a.width*n,u=a.height*n;const d=parseFloat(l.paddingTop)+parseFloat(l.paddingBottom),h=c-(parseFloat(l.paddingLeft)+parseFloat(l.paddingRight)),f=u-d;this.containerRect={width:c,height:u,innerWidth:h,innerHeight:f};const p=parseFloat(s.dataset.width||"")||(t=>{let e=0;return e=t instanceof HTMLImageElement?t.naturalWidth:t instanceof SVGElement?t.width.baseVal.value:Math.max(t.offsetWidth,t.scrollWidth),e||0})(s),g=parseFloat(s.dataset.height||"")||(t=>{let e=0;return e=t instanceof HTMLImageElement?t.naturalHeight:t instanceof SVGElement?t.height.baseVal.value:Math.max(t.offsetHeight,t.scrollHeight),e||0})(s);let m=this.option("width",p)||z,v=this.option("height",g)||z;const b=m===z,x=v===z;"number"!=typeof m&&(m=p),"number"!=typeof v&&(v=g),b&&(m=p*(v/g)),x&&(v=g/(p/m));let w=s.parentElement instanceof HTMLPictureElement?s.parentElement:s;this.option("transformParent")&&(w=w.parentElement||w);const E=w.getAttribute("style")||"";w.style.setProperty("transform","none","important"),r&&(w.style.width="",w.style.height=""),w.offsetHeight;const S=s.getBoundingClientRect();let T=S.width*n,C=S.height*n,_=T,P=C;T=Math.min(T,m),C=Math.min(C,v),r?({width:T,height:C}=((t,e,n,i)=>{const o=n/t,s=i/e,r=Math.min(o,s);return{width:t*=r,height:e*=r}})(m,v,T,C)):(T=Math.min(T,m),C=Math.min(C,v));let k=.5*(P-C),O=.5*(_-T);this.contentRect=Object.assign(Object.assign({},this.contentRect),{top:S.top-a.top+k,bottom:a.bottom-S.bottom+k,left:S.left-a.left+O,right:a.right-S.right+O,fitWidth:T,fitHeight:C,width:T,height:C,fullWidth:m,fullHeight:v}),w.style.cssText=E,r&&(w.style.width=`${T}px`,w.style.height=`${C}px`),this.setTransform(),!0!==t&&this.emit("refresh"),this.ignoreBounds||(i(this.targetScale)<i(this.minScale)?this.zoomTo(this.minScale,{friction:0}):this.targetScale>this.maxScale?this.zoomTo(this.maxScale,{friction:0}):this.state===y.Init||this.checkBounds().inBounds||this.requestTick()),this.updateControls()}calculateBounds(){const{contentWidth:t,contentHeight:e}=this.calculateContentDim(this.target),{targetScale:n,lockedAxis:o}=this,{fitWidth:s,fitHeight:r}=this.contentRect;let a=0,l=0,c=0,u=0;const d=this.option("infinite");if(!0===d||o&&d===o)a=-1/0,c=1/0,l=-1/0,u=1/0;else{let{containerRect:o,contentRect:d}=this,h=i(s*n,O),f=i(r*n,O),{innerWidth:p,innerHeight:g}=o;if(o.width===h&&(p=o.width),o.width===f&&(g=o.height),t>p){c=.5*(t-p),a=-1*c;let e=.5*(d.right-d.left);a+=e,c+=e}if(s>p&&t<p&&(a-=.5*(s-p),c-=.5*(s-p)),e>g){u=.5*(e-g),l=-1*u;let t=.5*(d.bottom-d.top);l+=t,u+=t}r>g&&e<g&&(a-=.5*(r-g),c-=.5*(r-g))}return{x:{min:a,max:c},y:{min:l,max:u}}}getBounds(){const t=this.option("bounds");return t!==z?t:this.calculateBounds()}updateControls(){const t=this,e=t.container,{panMode:n,contentRect:o,targetScale:s,minScale:r}=t;let a=r,c=t.option("click")||!1;c&&(a=t.getNextScale(c));let u=t.canZoomIn(),d=t.canZoomOut(),h=n===A&&!!this.option("touch"),f=d&&h;if(h&&(i(s)<i(r)&&!this.panOnlyZoomed&&(f=!0),(i(o.width,1)>i(o.fitWidth,1)||i(o.height,1)>i(o.fitHeight,1))&&(f=!0)),i(o.width*s,1)<i(o.fitWidth,1)&&(f=!1),n===M&&(f=!1),l(e,this.cn("isDraggable"),f),!this.option("zoom"))return;let p=u&&i(a)>i(s),g=!p&&!f&&d&&i(a)<i(s);l(e,this.cn("canZoomIn"),p),l(e,this.cn("canZoomOut"),g);for(const t of e.querySelectorAll("[data-panzoom-action]")){let e=!1,n=!1;switch(t.dataset.panzoomAction){case"zoomIn":u?e=!0:n=!0;break;case"zoomOut":d?e=!0:n=!0;break;case"toggleZoom":case"iterateZoom":u||d?e=!0:n=!0;const i=t.querySelector("g");i&&(i.style.display=u?"":"none")}e?(t.removeAttribute("disabled"),t.removeAttribute("tabindex")):n&&(t.setAttribute("disabled",""),t.setAttribute("tabindex","-1"))}}panTo({x:t=this.target.e,y:e=this.target.f,scale:n=this.targetScale,friction:i=this.option("friction"),angle:o=0,originX:s=0,originY:r=0,flipX:a=!1,flipY:l=!1,ignoreBounds:c=!1}){this.state!==y.Destroy&&this.applyChange({panX:t-this.target.e,panY:e-this.target.f,scale:n/this.targetScale,angle:o,originX:s,originY:r,friction:i,flipX:a,flipY:l,ignoreBounds:c})}applyChange({panX:t=0,panY:e=0,scale:n=1,angle:o=0,originX:s=-this.current.e,originY:r=-this.current.f,friction:a=this.option("friction"),flipX:l=!1,flipY:c=!1,ignoreBounds:u=!1,bounce:d=this.option("bounce")}){const h=this.state;if(h===y.Destroy)return;this.rAF&&(cancelAnimationFrame(this.rAF),this.rAF=null),this.friction=a||0,this.ignoreBounds=u;const{current:f}=this,p=f.e,g=f.f,m=this.getMatrix(this.target);let v=(new DOMMatrix).translate(p,g).translate(s,r).translate(t,e);if(this.option("zoom")){if(!u){const t=this.targetScale,e=this.minScale,i=this.maxScale;t*n<e&&(n=e/t),t*n>i&&(n=i/t)}v=v.scale(n)}v=v.translate(-s,-r).translate(-p,-g).multiply(m),o&&(v=v.rotate(o)),l&&(v=v.scale(-1,1)),c&&(v=v.scale(1,-1));for(const t of x)"e"!==t&&"f"!==t&&(v[t]>this.minScale+1e-5||v[t]<this.minScale-1e-5)?this.target[t]=v[t]:this.target[t]=i(v[t],O);(this.targetScale<this.scale||Math.abs(n-1)>.1||this.panMode===M||!1===d)&&!u&&this.clampTargetBounds(),h===y.Init?this.animate():this.isResting||(this.state=y.Panning,this.requestTick())}stop(t=!1){if(this.state===y.Init||this.state===y.Destroy)return;const e=this.isTicking;this.rAF&&(cancelAnimationFrame(this.rAF),this.rAF=null),this.isBouncingX=!1,this.isBouncingY=!1;for(const e of x)this.velocity[e]=0,"current"===t?this.current[e]=this.target[e]:"target"===t&&(this.target[e]=this.current[e]);this.setTransform(),_(this.container,"is-scaling"),_(this.container,"is-animating"),this.isTicking=!1,this.state=y.Ready,e&&(this.emit("endAnimation"),this.updateControls())}requestTick(){this.isTicking||(this.emit("startAnimation"),this.updateControls(),P(this.container,"is-animating"),this.isScaling&&P(this.container,"is-scaling")),this.isTicking=!0,this.rAF||(this.rAF=requestAnimationFrame((()=>this.animate())))}panWithMouse(t,e=this.option("mouseMoveFriction")){if(this.pmme=t,this.panMode!==M||!t)return;if(i(this.targetScale)<=i(this.minScale))return;this.emit("mouseMove",t);const{container:n,containerRect:o,contentRect:s}=this,r=o.width,a=o.height,l=n.getBoundingClientRect(),c=(t.clientX||0)-l.left,u=(t.clientY||0)-l.top;let{contentWidth:d,contentHeight:h}=this.calculateContentDim(this.target);const f=this.option("mouseMoveFactor");f>1&&(d!==r&&(d*=f),h!==a&&(h*=f));let p=.5*(d-r)-c/r*100/100*(d-r);p+=.5*(s.right-s.left);let g=.5*(h-a)-u/a*100/100*(h-a);g+=.5*(s.bottom-s.top),this.applyChange({panX:p-this.target.e,panY:g-this.target.f,friction:e})}zoomWithWheel(t){if(this.state===y.Destroy||this.state===y.Init)return;const e=Date.now();if(e-this.pwt<45)return void t.preventDefault();this.pwt=e;var n=[-t.deltaX||0,-t.deltaY||0,-t.detail||0].reduce((function(t,e){return Math.abs(e)>Math.abs(t)?e:t}));const o=Math.max(-1,Math.min(1,n)),{targetScale:s,maxScale:r,minScale:a}=this;let l=s*(100+45*o)/100;i(l)<i(a)&&i(s)<=i(a)?(this.cwd+=Math.abs(o),l=a):i(l)>i(r)&&i(s)>=i(r)?(this.cwd+=Math.abs(o),l=r):(this.cwd=0,l=Math.max(Math.min(l,r),a)),this.cwd>this.option("wheelLimit")||(t.preventDefault(),i(l)!==i(s)&&this.zoomTo(l,{event:t}))}canZoomIn(){return this.option("zoom")&&(i(this.contentRect.width,1)<i(this.contentRect.fitWidth,1)||i(this.targetScale)<i(this.maxScale))}canZoomOut(){return this.option("zoom")&&i(this.targetScale)>i(this.minScale)}zoomIn(t=1.25,e){this.zoomTo(this.targetScale*t,e)}zoomOut(t=.8,e){this.zoomTo(this.targetScale*t,e)}zoomToFit(t){this.zoomTo("fit",t)}zoomToCover(t){this.zoomTo("cover",t)}zoomToFull(t){this.zoomTo("full",t)}zoomToMax(t){this.zoomTo("max",t)}toggleZoom(t){this.zoomTo(this.getNextScale("toggleZoom"),t)}toggleMax(t){this.zoomTo(this.getNextScale("toggleMax"),t)}toggleCover(t){this.zoomTo(this.getNextScale("toggleCover"),t)}iterateZoom(t){this.zoomTo("next",t)}zoomTo(t=1,{friction:e=z,originX:n=z,originY:i=z,event:o}={}){if(this.isContentLoading||this.state===y.Destroy)return;const{targetScale:s,fullScale:r,maxScale:a,coverScale:l}=this;if(this.stop(),this.panMode===M&&(o=this.pmme||o),o||n===z||i===z){const t=this.content.getBoundingClientRect(),e=this.container.getBoundingClientRect(),s=o?o.clientX:e.left+.5*e.width,r=o?o.clientY:e.top+.5*e.height;n=s-t.left-.5*t.width,i=r-t.top-.5*t.height}let c=1;"number"==typeof t?c=t:"full"===t?c=r:"cover"===t?c=l:"max"===t?c=a:"fit"===t?c=1:"next"===t&&(c=this.getNextScale("iterateZoom")),c=c/s||1,e=e===z?c>1?.15:.25:e,this.applyChange({scale:c,originX:n,originY:i,friction:e}),o&&this.panMode===M&&this.panWithMouse(o,e)}rotateCCW(){this.applyChange({angle:-90})}rotateCW(){this.applyChange({angle:90})}flipX(){this.applyChange({flipX:!0})}flipY(){this.applyChange({flipY:!0})}fitX(){this.stop("target");const{containerRect:t,contentRect:e,target:n}=this;this.applyChange({panX:.5*t.width-(e.left+.5*e.fitWidth)-n.e,panY:.5*t.height-(e.top+.5*e.fitHeight)-n.f,scale:t.width/e.fitWidth/this.targetScale,originX:0,originY:0,ignoreBounds:!0})}fitY(){this.stop("target");const{containerRect:t,contentRect:e,target:n}=this;this.applyChange({panX:.5*t.width-(e.left+.5*e.fitWidth)-n.e,panY:.5*t.innerHeight-(e.top+.5*e.fitHeight)-n.f,scale:t.height/e.fitHeight/this.targetScale,originX:0,originY:0,ignoreBounds:!0})}toggleFS(){const{container:t}=this,e=this.cn("inFullscreen"),n=this.cn("htmlHasFullscreen");t.classList.toggle(e);const i=t.classList.contains(e);i?(document.documentElement.classList.add(n),document.addEventListener("keydown",this.onKeydown,!0)):(document.documentElement.classList.remove(n),document.removeEventListener("keydown",this.onKeydown,!0)),this.updateMetrics(),this.emit(i?"enterFS":"exitFS")}getMatrix(t=this.current){const{a:e,b:n,c:i,d:o,e:s,f:r}=t;return new DOMMatrix([e,n,i,o,s,r])}reset(t){if(this.state!==y.Init&&this.state!==y.Destroy){this.stop("current");for(const t of x)this.target[t]=k[t];this.target.a=this.minScale,this.target.d=this.minScale,this.clampTargetBounds(),this.isResting||(this.friction=void 0===t?this.option("friction"):t,this.state=y.Panning,this.requestTick())}}destroy(){this.stop(),this.state=y.Destroy,this.detachEvents(),this.detachObserver();const{container:t,content:e}=this,n=this.option("classes")||{};for(const e of Object.values(n))t.classList.remove(e+"");e&&(e.removeEventListener("load",this.onLoad),e.removeEventListener("error",this.onError)),this.detachPlugins()}}Object.defineProperty(I,"defaults",{enumerable:!0,configurable:!0,writable:!0,value:E}),Object.defineProperty(I,"Plugins",{enumerable:!0,configurable:!0,writable:!0,value:{}});const R=function(t,e){let n=!0;return(...i)=>{n&&(n=!1,t(...i),setTimeout((()=>{n=!0}),e))}},F=(t,e)=>{let n=[];return t.childNodes.forEach((t=>{t.nodeType!==Node.ELEMENT_NODE||e&&!t.matches(e)||n.push(t)})),n};var N;!function(t){t[t.Init=0]="Init",t[t.Ready=1]="Ready",t[t.Destroy=2]="Destroy"}(N||(N={}));const H=t=>{if("string"==typeof t||t instanceof HTMLElement)t={html:t};else{const e=t.thumb;void 0!==e&&("string"==typeof e&&(t.thumbSrc=e),e instanceof HTMLImageElement&&(t.thumbEl=e,t.thumbElSrc=e.src,t.thumbSrc=e.src),delete t.thumb)}return Object.assign({html:"",el:null,isDom:!1,class:"",customClass:"",index:-1,dim:0,gap:0,pos:0,transition:!1},t)},B=(t={})=>Object.assign({index:-1,slides:[],dim:0,pos:-1},t);class q extends v{constructor(t,e){super(e),Object.defineProperty(this,"instance",{enumerable:!0,configurable:!0,writable:!0,value:t})}attach(){}detach(){}}class $ extends q{constructor(){super(...arguments),Object.defineProperty(this,"isDynamic",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"list",{enumerable:!0,configurable:!0,writable:!0,value:null})}onRefresh(){this.refresh()}build(){let t=this.list;if(!t){t=document.createElement("ul"),P(t,this.cn("list")),t.setAttribute("role","tablist");const e=this.instance.container;e.appendChild(t),P(e,this.cn("hasDots")),this.list=t}return t}refresh(){var t;const e=this.instance.pages.length,n=Math.min(2,this.option("minCount")),i=Math.max(2e3,this.option("maxCount")),o=this.option("dynamicFrom");if(e<n||e>i)return void this.cleanup();const s="number"==typeof o&&e>5&&e>=o,r=!this.list||this.isDynamic!==s||this.list.children.length!==e;r&&this.cleanup();const a=this.build();if(l(a,this.cn("isDynamic"),!!s),r)for(let t=0;t<e;t++)a.append(this.createItem(t));let c,u=0;for(const e of[...a.children]){const n=u===this.instance.page;n&&(c=e),l(e,this.cn("isCurrent"),n),null===(t=e.children[0])||void 0===t||t.setAttribute("aria-selected",n?"true":"false");for(const t of["isBeforePrev","isPrev","isNext","isAfterNext"])_(e,this.cn(t));u++}if(c=c||a.firstChild,s&&c){const t=c.previousElementSibling,e=t&&t.previousElementSibling;P(t,this.cn("isPrev")),P(e,this.cn("isBeforePrev"));const n=c.nextElementSibling,i=n&&n.nextElementSibling;P(n,this.cn("isNext")),P(i,this.cn("isAfterNext"))}this.isDynamic=s}createItem(t=0){var e;const n=document.createElement("li");n.setAttribute("role","presentation");const i=r(this.instance.localize(this.option("dotTpl"),[["%d",t+1]]).replace(/\%i/g,t+""));return n.appendChild(i),null===(e=n.children[0])||void 0===e||e.setAttribute("role","tab"),n}cleanup(){this.list&&(this.list.remove(),this.list=null),this.isDynamic=!1,_(this.instance.container,this.cn("hasDots"))}attach(){this.instance.on(["refresh","change"],this.onRefresh)}detach(){this.instance.off(["refresh","change"],this.onRefresh),this.cleanup()}}Object.defineProperty($,"defaults",{enumerable:!0,configurable:!0,writable:!0,value:{classes:{list:"f-carousel__dots",isDynamic:"is-dynamic",hasDots:"has-dots",dot:"f-carousel__dot",isBeforePrev:"is-before-prev",isPrev:"is-prev",isCurrent:"is-current",isNext:"is-next",isAfterNext:"is-after-next"},dotTpl:'<button type="button" data-carousel-page="%i" aria-label="{{GOTO}}"><span class="f-carousel__dot" aria-hidden="true"></span></button>',dynamicFrom:11,maxCount:1/0,minCount:2}});const W="disabled",X="next",Y="prev";class U extends q{constructor(){super(...arguments),Object.defineProperty(this,"container",{enumerable:!0,configurable:!0,writable:!0,value:null}),Object.defineProperty(this,"prev",{enumerable:!0,configurable:!0,writable:!0,value:null}),Object.defineProperty(this,"next",{enumerable:!0,configurable:!0,writable:!0,value:null}),Object.defineProperty(this,"isDom",{enumerable:!0,configurable:!0,writable:!0,value:!1})}onRefresh(){const t=this.instance,e=t.pages.length,n=t.page;if(e<2)return void this.cleanup();this.build();let i=this.prev,o=this.next;i&&o&&(i.removeAttribute(W),o.removeAttribute(W),t.isInfinite||(n<=0&&i.setAttribute(W,""),n>=e-1&&o.setAttribute(W,"")))}addBtn(t){var e;const n=this.instance,i=document.createElement("button");i.setAttribute("tabindex","0"),i.setAttribute("title",n.localize(`{{${t.toUpperCase()}}}`)),P(i,this.cn("button")+" "+this.cn(t===X?"isNext":"isPrev"));const o=n.isRTL?t===X?Y:X:t;var s;return i.innerHTML=n.localize(this.option(`${o}Tpl`)),i.dataset[`carousel${s=t,s?s.match("^[a-z]")?s.charAt(0).toUpperCase()+s.substring(1):s:""}`]="true",null===(e=this.container)||void 0===e||e.appendChild(i),i}build(){const t=this.instance.container,e=this.cn("container");let{container:n,prev:i,next:o}=this;n||(n=t.querySelector("."+e),this.isDom=!!n),n||(n=document.createElement("div"),P(n,e),t.appendChild(n)),this.container=n,o||(o=n.querySelector("[data-carousel-next]")),o||(o=this.addBtn(X)),this.next=o,i||(i=n.querySelector("[data-carousel-prev]")),i||(i=this.addBtn(Y)),this.prev=i}cleanup(){this.isDom||(this.prev&&this.prev.remove(),this.next&&this.next.remove(),this.container&&this.container.remove()),this.prev=null,this.next=null,this.container=null,this.isDom=!1}attach(){this.instance.on(["refresh","change"],this.onRefresh)}detach(){this.instance.off(["refresh","change"],this.onRefresh),this.cleanup()}}Object.defineProperty(U,"defaults",{enumerable:!0,configurable:!0,writable:!0,value:{classes:{container:"f-carousel__nav",button:"f-button",isNext:"is-next",isPrev:"is-prev"},nextTpl:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" tabindex="-1"><path d="M9 3l9 9-9 9"/></svg>',prevTpl:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" tabindex="-1"><path d="M15 3l-9 9 9 9"/></svg>'}});class V extends q{constructor(){super(...arguments),Object.defineProperty(this,"selectedIndex",{enumerable:!0,configurable:!0,writable:!0,value:null}),Object.defineProperty(this,"target",{enumerable:!0,configurable:!0,writable:!0,value:null}),Object.defineProperty(this,"nav",{enumerable:!0,configurable:!0,writable:!0,value:null})}addAsTargetFor(t){this.target=this.instance,this.nav=t,this.attachEvents()}addAsNavFor(t){this.nav=this.instance,this.target=t,this.attachEvents()}attachEvents(){const{nav:t,target:e}=this;t&&e&&(t.options.initialSlide=e.options.initialPage,t.state===N.Ready?this.onNavReady(t):t.on("ready",this.onNavReady),e.state===N.Ready?this.onTargetReady(e):e.on("ready",this.onTargetReady))}onNavReady(t){t.on("createSlide",this.onNavCreateSlide),t.on("Panzoom.click",this.onNavClick),t.on("Panzoom.touchEnd",this.onNavTouch),this.onTargetChange()}onTargetReady(t){t.on("change",this.onTargetChange),t.on("Panzoom.refresh",this.onTargetChange),this.onTargetChange()}onNavClick(t,e,n){this.onNavTouch(t,t.panzoom,n)}onNavTouch(t,e,n){var i,o;if(Math.abs(e.dragOffset.x)>3||Math.abs(e.dragOffset.y)>3)return;const s=n.target,{nav:r,target:a}=this;if(!r||!a||!s)return;const l=s.closest("[data-index]");if(n.stopPropagation(),n.preventDefault(),!l)return;const c=parseInt(l.dataset.index||"",10)||0,u=a.getPageForSlide(c),d=r.getPageForSlide(c);r.slideTo(d),a.slideTo(u,{friction:(null===(o=null===(i=this.nav)||void 0===i?void 0:i.plugins)||void 0===o?void 0:o.Sync.option("friction"))||0}),this.markSelectedSlide(c)}onNavCreateSlide(t,e){e.index===this.selectedIndex&&this.markSelectedSlide(e.index)}onTargetChange(){var t,e;const{target:n,nav:i}=this;if(!n||!i)return;if(i.state!==N.Ready||n.state!==N.Ready)return;const o=null===(e=null===(t=n.pages[n.page])||void 0===t?void 0:t.slides[0])||void 0===e?void 0:e.index,s=i.getPageForSlide(o);this.markSelectedSlide(o),i.slideTo(s,null===i.prevPage&&null===n.prevPage?{friction:0}:void 0)}markSelectedSlide(t){const e=this.nav;e&&e.state===N.Ready&&(this.selectedIndex=t,[...e.slides].map((e=>{e.el&&e.el.classList[e.index===t?"add":"remove"]("is-nav-selected")})))}attach(){const t=this;let e=t.options.target,n=t.options.nav;e?t.addAsNavFor(e):n&&t.addAsTargetFor(n)}detach(){const t=this,e=t.nav,n=t.target;e&&(e.off("ready",t.onNavReady),e.off("createSlide",t.onNavCreateSlide),e.off("Panzoom.click",t.onNavClick),e.off("Panzoom.touchEnd",t.onNavTouch)),t.nav=null,n&&(n.off("ready",t.onTargetReady),n.off("refresh",t.onTargetChange),n.off("change",t.onTargetChange)),t.target=null}}Object.defineProperty(V,"defaults",{enumerable:!0,configurable:!0,writable:!0,value:{friction:.35}});const Z={Navigation:U,Dots:$,Sync:V},G="animationend",K="isSelected",J="slide";class Q extends b{get axis(){return this.isHorizontal?"e":"f"}get isEnabled(){return this.state===N.Ready}get isInfinite(){let t=!1;const{contentDim:e,viewportDim:n,pages:i,slides:o}=this,s=o[0];return i.length>=2&&s&&e+s.dim>=n&&(t=this.option("infinite")),t}get isRTL(){return"rtl"===this.option("direction")}get isHorizontal(){return"x"===this.option("axis")}constructor(t,e={},n={}){if(super(),Object.defineProperty(this,"bp",{enumerable:!0,configurable:!0,writable:!0,value:""}),Object.defineProperty(this,"lp",{enumerable:!0,configurable:!0,writable:!0,value:0}),Object.defineProperty(this,"userOptions",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"userPlugins",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"state",{enumerable:!0,configurable:!0,writable:!0,value:N.Init}),Object.defineProperty(this,"page",{enumerable:!0,configurable:!0,writable:!0,value:0}),Object.defineProperty(this,"prevPage",{enumerable:!0,configurable:!0,writable:!0,value:null}),Object.defineProperty(this,"container",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"viewport",{enumerable:!0,configurable:!0,writable:!0,value:null}),Object.defineProperty(this,"track",{enumerable:!0,configurable:!0,writable:!0,value:null}),Object.defineProperty(this,"slides",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"pages",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"panzoom",{enumerable:!0,configurable:!0,writable:!0,value:null}),Object.defineProperty(this,"inTransition",{enumerable:!0,configurable:!0,writable:!0,value:new Set}),Object.defineProperty(this,"contentDim",{enumerable:!0,configurable:!0,writable:!0,value:0}),Object.defineProperty(this,"viewportDim",{enumerable:!0,configurable:!0,writable:!0,value:0}),"string"==typeof t&&(t=document.querySelector(t)),!t||!C(t))throw new Error("No Element found");this.container=t,this.slideNext=R(this.slideNext.bind(this),150),this.slidePrev=R(this.slidePrev.bind(this),150),this.userOptions=e,this.userPlugins=n,queueMicrotask((()=>{this.processOptions()}))}processOptions(){var t,e;const n=g({},Q.defaults,this.userOptions);let i="";const o=n.breakpoints;if(o&&p(o))for(const[t,e]of Object.entries(o))window.matchMedia(t).matches&&p(e)&&(i+=t,g(n,e));i===this.bp&&this.state!==N.Init||(this.bp=i,this.state===N.Ready&&(n.initialSlide=(null===(e=null===(t=this.pages[this.page])||void 0===t?void 0:t.slides[0])||void 0===e?void 0:e.index)||0),this.state!==N.Init&&this.destroy(),super.setOptions(n),!1===this.option("enabled")?this.attachEvents():setTimeout((()=>{this.init()}),0))}init(){this.state=N.Init,this.emit("init"),this.attachPlugins(Object.assign(Object.assign({},Q.Plugins),this.userPlugins)),this.emit("attachPlugins"),this.initLayout(),this.initSlides(),this.updateMetrics(),this.setInitialPosition(),this.initPanzoom(),this.attachEvents(),this.state=N.Ready,this.emit("ready")}initLayout(){const{container:t}=this,e=this.option("classes");P(t,this.cn("container")),l(t,e.isLTR,!this.isRTL),l(t,e.isRTL,this.isRTL),l(t,e.isVertical,!this.isHorizontal),l(t,e.isHorizontal,this.isHorizontal);let n=this.option("viewport")||t.querySelector(`.${e.viewport}`);n||(n=document.createElement("div"),P(n,e.viewport),n.append(...F(t,`.${e.slide}`)),t.prepend(n)),n.addEventListener("scroll",this.onScroll);let i=this.option("track")||t.querySelector(`.${e.track}`);i||(i=document.createElement("div"),P(i,e.track),i.append(...Array.from(n.childNodes))),i.setAttribute("aria-live","polite"),n.contains(i)||n.prepend(i),this.viewport=n,this.track=i,this.emit("initLayout")}initSlides(){const{track:t}=this;if(!t)return;const e=[...this.slides],n=[];[...F(t,`.${this.cn(J)}`)].forEach((t=>{if(C(t)){const e=H({el:t,isDom:!0,index:this.slides.length});n.push(e)}}));for(let t of[...this.option("slides",[])||[],...e])n.push(H(t));this.slides=n;for(let t=0;t<this.slides.length;t++)this.slides[t].index=t;for(const t of n)this.emit("beforeInitSlide",t,t.index),this.emit("initSlide",t,t.index);this.emit("initSlides")}setInitialPage(){const t=this.option("initialSlide");this.page="number"==typeof t?this.getPageForSlide(t):parseInt(this.option("initialPage",0)+"",10)||0}setInitialPosition(){const{track:t,pages:e,isHorizontal:n}=this;if(!t||!e.length)return;let i=this.page;e[i]||(this.page=i=0);const o=(e[i].pos||0)*(this.isRTL&&n?1:-1),s=n?`${o}px`:"0",r=n?"0":`${o}px`;t.style.transform=`translate3d(${s}, ${r}, 0) scale(1)`,this.option("adaptiveHeight")&&this.setViewportHeight()}initPanzoom(){this.panzoom&&(this.panzoom.destroy(),this.panzoom=null);const t=this.option("Panzoom")||{};this.panzoom=new I(this.viewport,g({},{content:this.track,zoom:!1,panOnlyZoomed:!1,lockAxis:this.isHorizontal?"x":"y",infinite:this.isInfinite,click:!1,dblClick:!1,touch:t=>!(this.pages.length<2&&!t.options.infinite),bounds:()=>this.getBounds(),maxVelocity:t=>Math.abs(t.target[this.axis]-t.current[this.axis])<2*this.viewportDim?100:0},t)),this.panzoom.on("*",((t,e,...n)=>{this.emit(`Panzoom.${e}`,t,...n)})),this.panzoom.on("decel",this.onDecel),this.panzoom.on("refresh",this.onRefresh),this.panzoom.on("beforeTransform",this.onBeforeTransform),this.panzoom.on("endAnimation",this.onEndAnimation)}attachEvents(){const t=this.container;t&&(t.addEventListener("click",this.onClick,{passive:!1,capture:!1}),t.addEventListener("slideTo",this.onSlideTo)),window.addEventListener("resize",this.onResize)}createPages(){let t=[];const{contentDim:e,viewportDim:n}=this;let i=this.option("slidesPerPage");i=("auto"===i||e<=n)&&!1!==this.option("fill")?1/0:parseFloat(i+"");let o=0,s=0,r=0;for(const e of this.slides)(!t.length||s+e.dim-n>.05||r>=i)&&(t.push(B()),o=t.length-1,s=0,r=0),t[o].slides.push(e),s+=e.dim+e.gap,r++;return t}processPages(){const t=this.pages,{contentDim:e,viewportDim:n,isInfinite:o}=this,s=this.option("center"),r=this.option("fill"),a=r&&s&&e>n&&!o;if(t.forEach(((t,i)=>{var o;t.index=i,t.pos=(null===(o=t.slides[0])||void 0===o?void 0:o.pos)||0,t.dim=0;for(const[e,n]of t.slides.entries())t.dim+=n.dim,e<t.slides.length-1&&(t.dim+=n.gap);a&&t.pos+.5*t.dim<.5*n?t.pos=0:a&&t.pos+.5*t.dim>=e-.5*n?t.pos=e-n:s&&(t.pos+=-.5*(n-t.dim))})),t.forEach((t=>{r&&!o&&e>n&&(t.pos=Math.max(t.pos,0),t.pos=Math.min(t.pos,e-n)),t.pos=i(t.pos,1e3),t.dim=i(t.dim,1e3),Math.abs(t.pos)<=.1&&(t.pos=0)})),o)return t;const l=[];let c;return t.forEach((t=>{const e=Object.assign({},t);c&&e.pos===c.pos?(c.dim+=e.dim,c.slides=[...c.slides,...e.slides]):(e.index=l.length,c=e,l.push(e))})),l}getPageFromIndex(t=0){const e=this.pages.length;let n;return t=parseInt((t||0).toString())||0,n=this.isInfinite?(t%e+e)%e:Math.max(Math.min(t,e-1),0),n}getSlideMetrics(t){var e,n;const o=this.isHorizontal?"width":"height";let s=0,r=0,a=t.el;const l=!(!a||a.parentNode);if(a?s=parseFloat(a.dataset[o]||"")||0:(a=document.createElement("div"),a.style.visibility="hidden",(this.track||document.body).prepend(a)),P(a,this.cn(J)+" "+t.class+" "+t.customClass),s)a.style[o]=`${s}px`,a.style["width"===o?"height":"width"]="";else{l&&(this.track||document.body).prepend(a),s=a.getBoundingClientRect()[o]*Math.max(1,(null===(e=window.visualViewport)||void 0===e?void 0:e.scale)||1);let t=a[this.isHorizontal?"offsetWidth":"offsetHeight"];t-1>s&&(s=t)}const c=getComputedStyle(a);return"content-box"===c.boxSizing&&(this.isHorizontal?(s+=parseFloat(c.paddingLeft)||0,s+=parseFloat(c.paddingRight)||0):(s+=parseFloat(c.paddingTop)||0,s+=parseFloat(c.paddingBottom)||0)),r=parseFloat(c[this.isHorizontal?"marginRight":"marginBottom"])||0,l?null===(n=a.parentElement)||void 0===n||n.removeChild(a):t.el||a.remove(),{dim:i(s,1e3),gap:i(r,1e3)}}getBounds(){const{isInfinite:t,isRTL:e,isHorizontal:n,pages:i}=this;let o={min:0,max:0};if(t)o={min:-1/0,max:1/0};else if(i.length){const t=i[0].pos,s=i[i.length-1].pos;o=e&&n?{min:t,max:s}:{min:-1*s,max:-1*t}}return{x:n?o:{min:0,max:0},y:n?{min:0,max:0}:o}}repositionSlides(){let t,{isHorizontal:e,isRTL:n,isInfinite:o,viewport:s,viewportDim:r,contentDim:a,page:l,pages:c,slides:u,panzoom:d}=this,h=0,f=0,p=0,g=0;d?g=-1*d.current[this.axis]:c[l]&&(g=c[l].pos||0),t=e?n?"right":"left":"top",n&&e&&(g*=-1);for(const e of u){const n=e.el;n?("top"===t?(n.style.right="",n.style.left=""):n.style.top="",e.index!==h?n.style[t]=0===f?"":`${i(f,1e3)}px`:n.style[t]="",p+=e.dim+e.gap,h++):f+=e.dim+e.gap}if(o&&p&&s){let n=getComputedStyle(s),o="padding",l=e?"Right":"Bottom",c=parseFloat(n[o+(e?"Left":"Top")]);g-=c,r+=c,r+=parseFloat(n[o+l]);for(const e of u)e.el&&(i(e.pos)<i(r)&&i(e.pos+e.dim+e.gap)<i(g)&&i(g)>i(a-r)&&(e.el.style[t]=`${i(f+p,1e3)}px`),i(e.pos+e.gap)>=i(a-r)&&i(e.pos)>i(g+r)&&i(g)<i(r)&&(e.el.style[t]=`-${i(p,1e3)}px`))}let m,v,b=[...this.inTransition];if(b.length>1&&(m=c[b[0]],v=c[b[1]]),m&&v){let e=0;for(const n of u)n.el?this.inTransition.has(n.index)&&m.slides.indexOf(n)<0&&(n.el.style[t]=`${i(e+(m.pos-v.pos),1e3)}px`):e+=n.dim+n.gap}}createSlideEl(t){const{track:e,slides:n}=this;if(!e||!t)return;if(t.el&&t.el.parentNode)return;const i=t.el||document.createElement("div");P(i,this.cn(J)),P(i,t.class),P(i,t.customClass);const o=t.html;o&&(o instanceof HTMLElement?i.appendChild(o):i.innerHTML=t.html+"");const s=[];n.forEach(((t,e)=>{t.el&&s.push(e)}));const r=t.index;let a=null;s.length&&(a=n[s.reduce(((t,e)=>Math.abs(e-r)<Math.abs(t-r)?e:t))]);const l=a&&a.el&&a.el.parentNode?a.index<t.index?a.el.nextSibling:a.el:null;e.insertBefore(i,e.contains(l)?l:null),t.el=i,this.emit("createSlide",t)}removeSlideEl(t,e=!1){const n=null==t?void 0:t.el;if(!n||!n.parentNode)return;const i=this.cn(K);if(n.classList.contains(i)&&(_(n,i),this.emit("unselectSlide",t)),t.isDom&&!e)return n.removeAttribute("aria-hidden"),n.removeAttribute("data-index"),void(n.style.left="");this.emit("removeSlide",t);const o=new CustomEvent(G);n.dispatchEvent(o),t.el&&(t.el.remove(),t.el=null)}transitionTo(t=0,e=this.option("transition")){var n,i,o,s;if(!e)return!1;const r=this.page,{pages:a,panzoom:l}=this;t=parseInt((t||0).toString())||0;const c=this.getPageFromIndex(t);if(!l||!a[c]||a.length<2||Math.abs(((null===(i=null===(n=a[r])||void 0===n?void 0:n.slides[0])||void 0===i?void 0:i.dim)||0)-this.viewportDim)>1)return!1;let u=t>r?1:-1;this.isInfinite&&(0===r&&t===a.length-1&&(u=-1),r===a.length-1&&0===t&&(u=1));const d=a[c].pos*(this.isRTL?1:-1);if(r===c&&Math.abs(d-l.target[this.axis])<1)return!1;this.clearTransitions();const h=l.isResting;P(this.container,this.cn("inTransition"));const f=(null===(o=a[r])||void 0===o?void 0:o.slides[0])||null,p=(null===(s=a[c])||void 0===s?void 0:s.slides[0])||null;this.inTransition.add(p.index),this.createSlideEl(p);let g=f.el,m=p.el;h||e===J||(e="fadeFast",g=null);const v=this.isRTL?"next":"prev",b=this.isRTL?"prev":"next";return g&&(this.inTransition.add(f.index),f.transition=e,g.addEventListener(G,this.onAnimationEnd),g.classList.add(`f-${e}Out`,`to-${u>0?b:v}`)),m&&(p.transition=e,m.addEventListener(G,this.onAnimationEnd),m.classList.add(`f-${e}In`,`from-${u>0?v:b}`)),l.current[this.axis]=d,l.target[this.axis]=d,l.requestTick(),this.onChange(c),!0}manageSlideVisiblity(){const t=new Set,e=new Set,n=this.getVisibleSlides(parseFloat(this.option("preload",0)+"")||0);for(const i of this.slides)n.has(i)?t.add(i):e.add(i);for(const e of this.inTransition)t.add(this.slides[e]);for(const e of t)this.createSlideEl(e),this.lazyLoadSlide(e);for(const n of e)t.has(n)||this.removeSlideEl(n);this.markSelectedSlides(),this.repositionSlides()}markSelectedSlides(){if(!this.pages[this.page]||!this.pages[this.page].slides)return;const t="aria-hidden";let e=this.cn(K);if(e)for(const n of this.slides){const i=n.el;i&&(i.dataset.index=`${n.index}`,i.classList.contains("f-thumbs__slide")?this.getVisibleSlides(0).has(n)?i.removeAttribute(t):i.setAttribute(t,"true"):this.pages[this.page].slides.includes(n)?(i.classList.contains(e)||(P(i,e),this.emit("selectSlide",n)),i.removeAttribute(t)):(i.classList.contains(e)&&(_(i,e),this.emit("unselectSlide",n)),i.setAttribute(t,"true")))}}flipInfiniteTrack(){const{axis:t,isHorizontal:e,isInfinite:n,isRTL:i,viewportDim:o,contentDim:s}=this,r=this.panzoom;if(!r||!n)return;let a=r.current[t],l=r.target[t]-a,c=0,u=.5*o;i&&e?(a<-u&&(c=-1,a+=s),a>s-u&&(c=1,a-=s)):(a>u&&(c=1,a-=s),a<-s+u&&(c=-1,a+=s)),c&&(r.current[t]=a,r.target[t]=a+l)}lazyLoadImg(t,e){const n=this,i="f-fadeIn",o="is-preloading";let s=!1,a=null;const l=()=>{s||(s=!0,a&&(a.remove(),a=null),_(e,o),e.complete&&(P(e,i),setTimeout((()=>{_(e,i)}),350)),this.option("adaptiveHeight")&&t.el&&this.pages[this.page].slides.indexOf(t)>-1&&(n.updateMetrics(),n.setViewportHeight()),this.emit("load",t))};P(e,o),e.src=e.dataset.lazySrcset||e.dataset.lazySrc||"",delete e.dataset.lazySrc,delete e.dataset.lazySrcset,e.addEventListener("error",(()=>{l()})),e.addEventListener("load",(()=>{l()})),setTimeout((()=>{const n=e.parentNode;n&&t.el&&(e.complete?l():s||(a=r(T),n.insertBefore(a,e)))}),300)}lazyLoadSlide(t){const e=t&&t.el;if(!e)return;const n=new Set;let i=Array.from(e.querySelectorAll("[data-lazy-src],[data-lazy-srcset]"));e.dataset.lazySrc&&i.push(e),i.map((t=>{t instanceof HTMLImageElement?n.add(t):t instanceof HTMLElement&&t.dataset.lazySrc&&(t.style.backgroundImage=`url('${t.dataset.lazySrc}')`,delete t.dataset.lazySrc)}));for(const e of n)this.lazyLoadImg(t,e)}onAnimationEnd(t){var e;const n=t.target,i=n?parseInt(n.dataset.index||"",10)||0:-1,o=this.slides[i],s=t.animationName;if(!n||!o||!s)return;const r=!!this.inTransition.has(i)&&o.transition;r&&s.substring(0,r.length+2)===`f-${r}`&&this.inTransition.delete(i),this.inTransition.size||this.clearTransitions(),i===this.page&&(null===(e=this.panzoom)||void 0===e?void 0:e.isResting)&&this.emit("settle")}onDecel(t,e=0,n=0,i=0,o=0){if(this.option("dragFree"))return void this.setPageFromPosition();const{isRTL:s,isHorizontal:r,axis:a,pages:l}=this,c=l.length,u=Math.abs(Math.atan2(n,e)/(Math.PI/180));let d=0;if(d=u>45&&u<135?r?0:n:r?e:0,!c)return;let h=this.page,f=s&&r?1:-1;const p=t.current[a]*f;let{pageIndex:g}=this.getPageFromPosition(p);Math.abs(d)>5?(l[h].dim<document.documentElement["client"+(this.isHorizontal?"Width":"Height")]-1&&(h=g),h=s&&r?d<0?h-1:h+1:d<0?h+1:h-1):h=0===i&&0===o?h:g,this.slideTo(h,{transition:!1,friction:t.option("decelFriction")})}onClick(t){const e=t.target,n=e&&C(e)?e.dataset:null;let i,o;n&&(void 0!==n.carouselPage?(o="slideTo",i=n.carouselPage):void 0!==n.carouselNext?o="slideNext":void 0!==n.carouselPrev&&(o="slidePrev")),o?(t.preventDefault(),t.stopPropagation(),e&&!e.hasAttribute("disabled")&&this[o](i)):this.emit("click",t)}onSlideTo(t){const e=t.detail||0;this.slideTo(this.getPageForSlide(e),{friction:0})}onChange(t,e=0){const n=this.page;this.prevPage=n,this.page=t,this.option("adaptiveHeight")&&this.setViewportHeight(),t!==n&&(this.markSelectedSlides(),this.emit("change",t,n,e))}onRefresh(){let t=this.contentDim,e=this.viewportDim;this.updateMetrics(),this.contentDim===t&&this.viewportDim===e||this.slideTo(this.page,{friction:0,transition:!1})}onScroll(){var t;null===(t=this.viewport)||void 0===t||t.scroll(0,0)}onResize(){this.option("breakpoints")&&this.processOptions()}onBeforeTransform(t){this.lp!==t.current[this.axis]&&(this.flipInfiniteTrack(),this.manageSlideVisiblity()),this.lp=t.current.e}onEndAnimation(){this.inTransition.size||this.emit("settle")}reInit(t=null,e=null){this.destroy(),this.state=N.Init,this.prevPage=null,this.userOptions=t||this.userOptions,this.userPlugins=e||this.userPlugins,this.processOptions()}slideTo(t=0,{friction:e=this.option("friction"),transition:n=this.option("transition")}={}){if(this.state===N.Destroy)return;t=parseInt((t||0).toString())||0;const i=this.getPageFromIndex(t),{axis:o,isHorizontal:s,isRTL:r,pages:a,panzoom:l}=this,c=a.length,u=r&&s?1:-1;if(!l||!c)return;if(this.page!==i){const e=new Event("beforeChange",{bubbles:!0,cancelable:!0});if(this.emit("beforeChange",e,t),e.defaultPrevented)return}if(this.transitionTo(t,n))return;let d=a[i].pos;if(this.isInfinite){const e=this.contentDim,n=l.target[o]*u;2===c?d+=e*Math.floor(parseFloat(t+"")/2):d=[d,d-e,d+e].reduce((function(t,e){return Math.abs(e-n)<Math.abs(t-n)?e:t}))}d*=u,Math.abs(l.target[o]-d)<1||(l.panTo({x:s?d:0,y:s?0:d,friction:e}),this.onChange(i))}slideToClosest(t){if(this.panzoom){const{pageIndex:e}=this.getPageFromPosition();this.slideTo(e,t)}}slideNext(){this.slideTo(this.page+1)}slidePrev(){this.slideTo(this.page-1)}clearTransitions(){this.inTransition.clear(),_(this.container,this.cn("inTransition"));const t=["to-prev","to-next","from-prev","from-next"];for(const e of this.slides){const n=e.el;if(n){n.removeEventListener(G,this.onAnimationEnd),n.classList.remove(...t);const i=e.transition;i&&n.classList.remove(`f-${i}Out`,`f-${i}In`)}}this.manageSlideVisiblity()}addSlide(t,e){var n,i,o,s;const r=this.panzoom,a=(null===(n=this.pages[this.page])||void 0===n?void 0:n.pos)||0,l=(null===(i=this.pages[this.page])||void 0===i?void 0:i.dim)||0,c=this.contentDim<this.viewportDim;let u=Array.isArray(e)?e:[e];const d=[];for(const t of u)d.push(H(t));this.slides.splice(t,0,...d);for(let t=0;t<this.slides.length;t++)this.slides[t].index=t;for(const t of d)this.emit("beforeInitSlide",t,t.index);if(this.page>=t&&(this.page+=d.length),this.updateMetrics(),r){const e=(null===(o=this.pages[this.page])||void 0===o?void 0:o.pos)||0,n=(null===(s=this.pages[this.page])||void 0===s?void 0:s.dim)||0,i=this.pages.length||1,u=this.isRTL?l-n:n-l,d=this.isRTL?a-e:e-a;c&&1===i?(t<=this.page&&(r.current[this.axis]-=u,r.target[this.axis]-=u),r.panTo({[this.isHorizontal?"x":"y"]:-1*e})):d&&t<=this.page&&(r.target[this.axis]-=d,r.current[this.axis]-=d,r.requestTick())}for(const t of d)this.emit("initSlide",t,t.index)}prependSlide(t){this.addSlide(0,t)}appendSlide(t){this.addSlide(this.slides.length,t)}removeSlide(t){const e=this.slides.length;t=(t%e+e)%e;const n=this.slides[t];if(n){this.removeSlideEl(n,!0),this.slides.splice(t,1);for(let t=0;t<this.slides.length;t++)this.slides[t].index=t;this.updateMetrics(),this.slideTo(this.page,{friction:0,transition:!1}),this.emit("destroySlide",n)}}updateMetrics(){const{panzoom:t,viewport:e,track:n,slides:o,isHorizontal:s,isInfinite:r}=this;if(!n)return;const a=s?"width":"height",l=s?"offsetWidth":"offsetHeight";if(e){let t=Math.max(e[l],i(e.getBoundingClientRect()[a],1e3)),n=getComputedStyle(e),o="padding",r=s?"Right":"Bottom";t-=parseFloat(n[o+(s?"Left":"Top")])+parseFloat(n[o+r]),this.viewportDim=t}let c,u=0;for(const[t,e]of o.entries()){let n=0,s=0;!e.el&&c?(n=c.dim,s=c.gap):(({dim:n,gap:s}=this.getSlideMetrics(e)),c=e),n=i(n,1e3),s=i(s,1e3),e.dim=n,e.gap=s,e.pos=u,u+=n,(r||t<o.length-1)&&(u+=s)}u=i(u,1e3),this.contentDim=u,t&&(t.contentRect[a]=u,t.contentRect[s?"fullWidth":"fullHeight"]=u),this.pages=this.createPages(),this.pages=this.processPages(),this.state===N.Init&&this.setInitialPage(),this.page=Math.max(0,Math.min(this.page,this.pages.length-1)),this.manageSlideVisiblity(),this.emit("refresh")}getProgress(t,e=!1,n=!1){void 0===t&&(t=this.page);const o=this,s=o.panzoom,r=o.contentDim,a=o.pages[t]||0;if(!a||!s)return t>this.page?-1:1;let l=-1*s.current.e,c=i((l-a.pos)/(1*a.dim),1e3),u=c,d=c;this.isInfinite&&!0!==n&&(u=i((l-a.pos+r)/(1*a.dim),1e3),d=i((l-a.pos-r)/(1*a.dim),1e3));let h=[c,u,d].reduce((function(t,e){return Math.abs(e)<Math.abs(t)?e:t}));return e?h:h>1?1:h<-1?-1:h}setViewportHeight(){const{page:t,pages:e,viewport:n,isHorizontal:i}=this;if(!n||!e[t])return;let o=0;i&&this.track&&(this.track.style.height="auto",e[t].slides.forEach((t=>{t.el&&(o=Math.max(o,t.el.offsetHeight))}))),n.style.height=o?`${o}px`:""}getPageForSlide(t){for(const e of this.pages)for(const n of e.slides)if(n.index===t)return e.index;return-1}getVisibleSlides(t=0){var e;const n=new Set;let{panzoom:i,contentDim:o,viewportDim:s,pages:r,page:a}=this;if(s){o=o+(null===(e=this.slides[this.slides.length-1])||void 0===e?void 0:e.gap)||0;let l=0;l=i&&i.state!==y.Init&&i.state!==y.Destroy?-1*i.current[this.axis]:r[a]&&r[a].pos||0,this.isInfinite&&(l-=Math.floor(l/o)*o),this.isRTL&&this.isHorizontal&&(l*=-1);const c=l-s*t,u=l+s*(t+1),d=this.isInfinite?[-1,0,1]:[0];for(const t of this.slides)for(const e of d){const i=t.pos+e*o,s=i+t.dim+t.gap;i<u&&s>c&&n.add(t)}}return n}getPageFromPosition(t){const{viewportDim:e,contentDim:n,slides:i,pages:o,panzoom:s}=this,r=o.length,a=i.length,l=i[0],c=i[a-1],u=this.option("center");let d=0,h=0,f=0,p=void 0===t?-1*((null==s?void 0:s.target[this.axis])||0):t;u&&(p+=.5*e),this.isInfinite?(p<l.pos-.5*c.gap&&(p-=n,f=-1),p>c.pos+c.dim+.5*c.gap&&(p-=n,f=1)):p=Math.max(l.pos||0,Math.min(p,c.pos));let g=c,m=i.find((t=>{const e=t.pos-.5*g.gap,n=t.pos+t.dim+.5*t.gap;return g=t,p>=e&&p<n}));return m||(m=c),h=this.getPageForSlide(m.index),d=h+f*r,{page:d,pageIndex:h}}setPageFromPosition(){const{pageIndex:t}=this.getPageFromPosition();this.onChange(t)}destroy(){if([N.Destroy].includes(this.state))return;this.state=N.Destroy;const{container:t,viewport:e,track:n,slides:i,panzoom:o}=this,s=this.option("classes");t.removeEventListener("click",this.onClick,{passive:!1,capture:!1}),t.removeEventListener("slideTo",this.onSlideTo),window.removeEventListener("resize",this.onResize),o&&(o.destroy(),this.panzoom=null),i&&i.forEach((t=>{this.removeSlideEl(t)})),this.detachPlugins(),e&&(e.removeEventListener("scroll",this.onScroll),e.offsetParent&&n&&n.offsetParent&&e.replaceWith(...n.childNodes));for(const[e,n]of Object.entries(s))"container"!==e&&n&&t.classList.remove(n);this.track=null,this.viewport=null,this.page=0,this.slides=[];const r=this.events.get("ready");this.events=new Map,r&&this.events.set("ready",r)}}Object.defineProperty(Q,"Panzoom",{enumerable:!0,configurable:!0,writable:!0,value:I}),Object.defineProperty(Q,"defaults",{enumerable:!0,configurable:!0,writable:!0,value:{viewport:null,track:null,enabled:!0,slides:[],axis:"x",transition:"fade",preload:1,slidesPerPage:"auto",initialPage:0,friction:.12,Panzoom:{decelFriction:.12},center:!0,infinite:!0,fill:!0,dragFree:!1,adaptiveHeight:!1,direction:"ltr",classes:{container:"f-carousel",viewport:"f-carousel__viewport",track:"f-carousel__track",slide:"f-carousel__slide",isLTR:"is-ltr",isRTL:"is-rtl",isHorizontal:"is-horizontal",isVertical:"is-vertical",inTransition:"in-transition",isSelected:"is-selected"},l10n:{NEXT:"Next slide",PREV:"Previous slide",GOTO:"Go to slide #%d"}}}),Object.defineProperty(Q,"Plugins",{enumerable:!0,configurable:!0,writable:!0,value:Z});const tt=function(t){if(!C(t))return 0;const e=window.scrollY,n=window.innerHeight,i=e+n,o=t.getBoundingClientRect(),s=o.y+e,r=o.height,a=s+r;if(e>a||i<s)return 0;if(e<s&&i>a)return 100;if(s<e&&a>i)return 100;let l=r;s<e&&(l-=e-s),a>i&&(l-=a-i);const c=l/n*100;return Math.round(c)},et=!("undefined"==typeof window||!window.document||!window.document.createElement);let nt;const it=["a[href]","area[href]",'input:not([disabled]):not([type="hidden"]):not([aria-hidden])',"select:not([disabled]):not([aria-hidden])","textarea:not([disabled]):not([aria-hidden])","button:not([disabled]):not([aria-hidden]):not(.fancybox-focus-guard)","iframe","object","embed","video","audio","[contenteditable]",'[tabindex]:not([tabindex^="-"]):not([disabled]):not([aria-hidden])'].join(","),ot=t=>{if(t&&et){void 0===nt&&document.createElement("div").focus({get preventScroll(){return nt=!0,!1}});try{if(nt)t.focus({preventScroll:!0});else{const e=window.scrollY||document.body.scrollTop,n=window.scrollX||document.body.scrollLeft;t.focus(),document.body.scrollTo({top:e,left:n,behavior:"auto"})}}catch(t){}}},st=()=>{const t=document;let e,n="",i="",o="";return t.fullscreenEnabled?(n="requestFullscreen",i="exitFullscreen",o="fullscreenElement"):t.webkitFullscreenEnabled&&(n="webkitRequestFullscreen",i="webkitExitFullscreen",o="webkitFullscreenElement"),n&&(e={request:function(e=t.documentElement){return"webkitRequestFullscreen"===n?e[n](Element.ALLOW_KEYBOARD_INPUT):e[n]()},exit:function(){return t[o]&&t[i]()},isFullscreen:function(){return t[o]}}),e},rt={animated:!0,autoFocus:!0,backdropClick:"close",Carousel:{classes:{container:"fancybox__carousel",viewport:"fancybox__viewport",track:"fancybox__track",slide:"fancybox__slide"}},closeButton:"auto",closeExisting:!1,commonCaption:!1,compact:()=>window.matchMedia("(max-width: 578px), (max-height: 578px)").matches,contentClick:"toggleZoom",contentDblClick:!1,defaultType:"image",defaultDisplay:"block",dragToClose:!0,Fullscreen:{autoStart:!1},groupAll:!1,groupAttr:"data-fancybox",hideClass:"f-fadeOut",hideScrollbar:!0,idle:3500,keyboard:{Escape:"close",Delete:"close",Backspace:"close",PageUp:"next",PageDown:"prev",ArrowUp:"prev",ArrowDown:"next",ArrowRight:"next",ArrowLeft:"prev"},l10n:Object.assign(Object.assign({},w),{CLOSE:"Close",NEXT:"Next",PREV:"Previous",MODAL:"You can close this modal content with the ESC key",ERROR:"Something Went Wrong, Please Try Again Later",IMAGE_ERROR:"Image Not Found",ELEMENT_NOT_FOUND:"HTML Element Not Found",AJAX_NOT_FOUND:"Error Loading AJAX : Not Found",AJAX_FORBIDDEN:"Error Loading AJAX : Forbidden",IFRAME_ERROR:"Error Loading Page",TOGGLE_ZOOM:"Toggle zoom level",TOGGLE_THUMBS:"Toggle thumbnails",TOGGLE_SLIDESHOW:"Toggle slideshow",TOGGLE_FULLSCREEN:"Toggle full-screen mode",DOWNLOAD:"Download"}),parentEl:null,placeFocusBack:!0,showClass:"f-zoomInUp",startIndex:0,tpl:{closeButton:'<button data-fancybox-close class="f-button is-close-btn" title="{{CLOSE}}"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" tabindex="-1"><path d="M20 20L4 4m16 0L4 20"/></svg></button>',main:'<div class="fancybox__container" role="dialog" aria-modal="true" aria-label="{{MODAL}}" tabindex="-1">\n    <div class="fancybox__backdrop"></div>\n    <div class="fancybox__carousel"></div>\n    <div class="fancybox__footer"></div>\n  </div>'},trapFocus:!0,wheel:"zoom"};var at,lt;!function(t){t[t.Init=0]="Init",t[t.Ready=1]="Ready",t[t.Closing=2]="Closing",t[t.CustomClosing=3]="CustomClosing",t[t.Destroy=4]="Destroy"}(at||(at={})),function(t){t[t.Loading=0]="Loading",t[t.Opening=1]="Opening",t[t.Ready=2]="Ready",t[t.Closing=3]="Closing"}(lt||(lt={}));let ct="",ut=!1,dt=!1,ht=null;const ft=()=>{let t="",e="";const n=_e.getInstance();if(n){const i=n.carousel,o=n.getSlide();if(i&&o){let s=o.slug||void 0,r=o.triggerEl||void 0;e=s||n.option("slug")||"",!e&&r&&r.dataset&&(e=r.dataset.fancybox||""),e&&"true"!==e&&(t="#"+e+(!s&&i.slides.length>1?"-"+(o.index+1):""))}}return{hash:t,slug:e,index:1}},pt=()=>{const t=new URL(document.URL).hash,e=t.slice(1).split("-"),n=e[e.length-1],i=n&&/^\+?\d+$/.test(n)&&parseInt(e.pop()||"1",10)||1;return{hash:t,slug:e.join("-"),index:i}},gt=()=>{const{slug:t,index:e}=pt();if(!t)return;let n=document.querySelector(`[data-slug="${t}"]`);if(n&&n.dispatchEvent(new CustomEvent("click",{bubbles:!0,cancelable:!0})),_e.getInstance())return;const i=document.querySelectorAll(`[data-fancybox="${t}"]`);i.length&&(n=i[e-1],n&&n.dispatchEvent(new CustomEvent("click",{bubbles:!0,cancelable:!0})))},mt=()=>{if(!1===_e.defaults.Hash)return;const t=_e.getInstance();if(!1===(null==t?void 0:t.options.Hash))return;const{slug:e,index:n}=pt(),{slug:i}=ft();t&&(e===i?t.jumpTo(n-1):(ut=!0,t.close())),gt()},vt=()=>{ht&&clearTimeout(ht),queueMicrotask((()=>{mt()}))},bt=()=>{window.addEventListener("hashchange",vt,!1),setTimeout((()=>{mt()}),500)};et&&(/complete|interactive|loaded/.test(document.readyState)?bt():document.addEventListener("DOMContentLoaded",bt));const yt="is-zooming-in";class xt extends q{onCreateSlide(t,e,n){const i=this.instance.optionFor(n,"src")||"";n.el&&"image"===n.type&&"string"==typeof i&&this.setImage(n,i)}onRemoveSlide(t,e,n){n.panzoom&&n.panzoom.destroy(),n.panzoom=void 0,n.imageEl=void 0}onChange(t,e,n,i){_(this.instance.container,yt);for(const t of e.slides){const e=t.panzoom;e&&t.index!==n&&e.reset(.35)}}onClose(){var t;const e=this.instance,n=e.container,i=e.getSlide();if(!n||!n.parentElement||!i)return;const{el:o,contentEl:s,panzoom:r,thumbElSrc:a}=i;if(!o||!a||!s||!r||r.isContentLoading||r.state===y.Init||r.state===y.Destroy)return;r.updateMetrics();let l=this.getZoomInfo(i);if(!l)return;this.instance.state=at.CustomClosing,n.classList.remove(yt),n.classList.add("is-zooming-out"),s.style.backgroundImage=`url('${a}')`;const c=n.getBoundingClientRect();1===((null===(t=window.visualViewport)||void 0===t?void 0:t.scale)||1)&&Object.assign(n.style,{position:"absolute",top:`${n.offsetTop+window.scrollY}px`,left:`${n.offsetLeft+window.scrollX}px`,bottom:"auto",right:"auto",width:`${c.width}px`,height:`${c.height}px`,overflow:"hidden"});const{x:u,y:d,scale:h,opacity:f}=l;if(f){const t=((t,e,n,i)=>{const o=e-t;return e=>1+((e-t)/o*-1||0)})(r.scale,h);r.on("afterTransform",(()=>{s.style.opacity=t(r.scale)+""}))}r.on("endAnimation",(()=>{e.destroy()})),r.target.a=h,r.target.b=0,r.target.c=0,r.target.d=h,r.panTo({x:u,y:d,scale:h,friction:f?.2:.33,ignoreBounds:!0}),r.isResting&&e.destroy()}setImage(t,e){const n=this.instance;t.src=e,this.process(t,e).then((e=>{const{contentEl:i,imageEl:o,thumbElSrc:s,el:r}=t;if(n.isClosing()||!i||!o)return;i.offsetHeight;const a=!!n.isOpeningSlide(t)&&this.getZoomInfo(t);if(this.option("protected")&&r){r.addEventListener("contextmenu",(t=>{t.preventDefault()}));const t=document.createElement("div");P(t,"fancybox-protected"),i.appendChild(t)}if(s&&a){const o=e.contentRect,r=Math.max(o.fullWidth,o.fullHeight);let c=null;!a.opacity&&r>1200&&(c=document.createElement("img"),P(c,"fancybox-ghost"),c.src=s,i.appendChild(c));const u=()=>{c&&(P(c,"f-fadeFastOut"),setTimeout((()=>{c&&(c.remove(),c=null)}),200))};(l=s,new Promise(((t,e)=>{const n=new Image;n.onload=t,n.onerror=e,n.src=l}))).then((()=>{n.hideLoading(t),t.state=lt.Opening,this.instance.emit("reveal",t),this.zoomIn(t).then((()=>{u(),this.instance.done(t)}),(()=>{})),c&&setTimeout((()=>{u()}),r>2500?800:200)}),(()=>{n.hideLoading(t),n.revealContent(t)}))}else{const i=this.optionFor(t,"initialSize"),o=this.optionFor(t,"zoom"),s={event:n.prevMouseMoveEvent||n.options.event,friction:o?.12:0};let r=n.optionFor(t,"showClass")||void 0,a=!0;n.isOpeningSlide(t)&&("full"===i?e.zoomToFull(s):"cover"===i?e.zoomToCover(s):"max"===i?e.zoomToMax(s):a=!1,e.stop("current")),a&&r&&(r=e.isDragging?"f-fadeIn":""),n.hideLoading(t),n.revealContent(t,r)}var l}),(()=>{n.setError(t,"{{IMAGE_ERROR}}")}))}process(t,e){return new Promise(((n,i)=>{var o;const s=this.instance,a=t.el;s.clearContent(t),s.showLoading(t);let l=this.optionFor(t,"content");if("string"==typeof l&&(l=r(l)),!l||!C(l)){if(l=document.createElement("img"),l instanceof HTMLImageElement){let n="",i=t.caption;n="string"==typeof i&&i?i.replace(/<[^>]+>/gi,"").substring(0,1e3):`Image ${t.index+1} of ${(null===(o=s.carousel)||void 0===o?void 0:o.pages.length)||1}`,l.src=e||"",l.alt=n,l.draggable=!1,t.srcset&&l.setAttribute("srcset",t.srcset),this.instance.isOpeningSlide(t)&&(l.fetchPriority="high")}t.sizes&&l.setAttribute("sizes",t.sizes)}P(l,"fancybox-image"),t.imageEl=l,s.setContent(t,l,!1),t.panzoom=new I(a,g({transformParent:!0},this.option("Panzoom")||{},{content:l,width:(e,n)=>s.optionFor(t,"width","auto",n)||"auto",height:(e,n)=>s.optionFor(t,"height","auto",n)||"auto",wheel:()=>{const t=s.option("wheel");return("zoom"===t||"pan"==t)&&t},click:(e,n)=>{var i,o;if(s.isCompact||s.isClosing())return!1;if(t.index!==(null===(i=s.getSlide())||void 0===i?void 0:i.index))return!1;if(n){const t=n.composedPath()[0];if(["A","BUTTON","TEXTAREA","OPTION","INPUT","SELECT","VIDEO"].includes(t.nodeName))return!1}let r=!n||n.target&&(null===(o=t.contentEl)||void 0===o?void 0:o.contains(n.target));return s.option(r?"contentClick":"backdropClick")||!1},dblClick:()=>s.isCompact?"toggleZoom":s.option("contentDblClick")||!1,spinner:!1,panOnlyZoomed:!0,wheelLimit:1/0,on:{ready:t=>{n(t)},error:()=>{i()},destroy:()=>{i()}}}))}))}zoomIn(t){return new Promise(((e,n)=>{const i=this.instance,o=i.container,{panzoom:s,contentEl:r,el:a}=t;s&&s.updateMetrics();const l=this.getZoomInfo(t);if(!(l&&a&&r&&s&&o))return void n();const{x:c,y:u,scale:d,opacity:h}=l,f=()=>{t.state!==lt.Closing&&(h&&(r.style.opacity=Math.max(Math.min(1,1-(1-s.scale)/(1-d)),0)+""),s.scale>=1&&s.scale>s.targetScale-.1&&e(s))},p=t=>{(t.scale<.99||t.scale>1.01)&&!t.isDragging||(_(o,yt),r.style.opacity="",t.off("endAnimation",p),t.off("touchStart",p),t.off("afterTransform",f),e(t))};s.on("endAnimation",p),s.on("touchStart",p),s.on("afterTransform",f),s.on(["error","destroy"],(()=>{n()})),s.panTo({x:c,y:u,scale:d,friction:0,ignoreBounds:!0}),s.stop("current");const g={event:"mousemove"===s.panMode?i.prevMouseMoveEvent||i.options.event:void 0},m=this.optionFor(t,"initialSize");P(o,yt),i.hideLoading(t),"full"===m?s.zoomToFull(g):"cover"===m?s.zoomToCover(g):"max"===m?s.zoomToMax(g):s.reset(.172)}))}getZoomInfo(t){const{el:e,imageEl:n,thumbEl:i,panzoom:o}=t,s=this.instance,r=s.container;if(!e||!n||!i||!o||tt(i)<3||!this.optionFor(t,"zoom")||!r||s.state===at.Destroy)return!1;if("0"===getComputedStyle(r).getPropertyValue("--f-images-zoom"))return!1;const a=window.visualViewport||null;if(1!==(a?a.scale:1))return!1;let{top:l,left:c,width:u,height:d}=i.getBoundingClientRect(),{top:h,left:f,fitWidth:p,fitHeight:g}=o.contentRect;if(!(u&&d&&p&&g))return!1;const m=o.container.getBoundingClientRect();f+=m.left,h+=m.top;const v=-1*(f+.5*p-(c+.5*u)),b=-1*(h+.5*g-(l+.5*d)),y=u/p;let x=this.option("zoomOpacity")||!1;return"auto"===x&&(x=Math.abs(u/d-p/g)>.1),{x:v,y:b,scale:y,opacity:x}}attach(){const t=this,e=t.instance;e.on("Carousel.change",t.onChange),e.on("Carousel.createSlide",t.onCreateSlide),e.on("Carousel.removeSlide",t.onRemoveSlide),e.on("close",t.onClose)}detach(){const t=this,e=t.instance;e.off("Carousel.change",t.onChange),e.off("Carousel.createSlide",t.onCreateSlide),e.off("Carousel.removeSlide",t.onRemoveSlide),e.off("close",t.onClose)}}Object.defineProperty(xt,"defaults",{enumerable:!0,configurable:!0,writable:!0,value:{initialSize:"fit",Panzoom:{maxScale:1},protected:!1,zoom:!0,zoomOpacity:"auto"}}),"function"==typeof SuppressedError&&SuppressedError;const wt="html",Et="image",St="map",Tt="youtube",Ct="vimeo",_t="html5video",Pt=(t,e={})=>{const n=new URL(t),i=new URLSearchParams(n.search),o=new URLSearchParams;for(const[t,n]of[...i,...Object.entries(e)]){let e=n+"";if("t"===t){let t=e.match(/((\d*)m)?(\d*)s?/);t&&o.set("start",60*parseInt(t[2]||"0")+parseInt(t[3]||"0")+"")}else o.set(t,e)}let s=o+"",r=t.match(/#t=((.*)?\d+s)/);return r&&(s+=`#t=${r[1]}`),s},kt=["image","html","ajax","inline","clone","iframe","map","pdf","html5video","youtube","vimeo"];class Ot extends q{onBeforeInitSlide(t,e,n){this.processType(n)}onCreateSlide(t,e,n){this.setContent(n)}onClearContent(t,e){e.xhr&&(e.xhr.abort(),e.xhr=null);const n=e.iframeEl;n&&(n.onload=n.onerror=null,n.src="//about:blank",e.iframeEl=null);const i=e.contentEl,o=e.placeholderEl;if("inline"===e.type&&i&&o)i.classList.remove("fancybox__content"),"none"!==i.style.display&&(i.style.display="none"),o.parentNode&&o.parentNode.insertBefore(i,o),o.remove(),e.contentEl=void 0,e.placeholderEl=void 0;else for(;e.el&&e.el.firstChild;)e.el.removeChild(e.el.firstChild)}onSelectSlide(t,e,n){n.state===lt.Ready&&this.playVideo()}onUnselectSlide(t,e,n){var i,o;if(n.type===_t){try{null===(o=null===(i=n.el)||void 0===i?void 0:i.querySelector("video"))||void 0===o||o.pause()}catch(t){}return}let s;n.type===Ct?s={method:"pause",value:"true"}:n.type===Tt&&(s={event:"command",func:"pauseVideo"}),s&&n.iframeEl&&n.iframeEl.contentWindow&&n.iframeEl.contentWindow.postMessage(JSON.stringify(s),"*"),n.poller&&clearTimeout(n.poller)}onDone(t,e){t.isCurrentSlide(e)&&!t.isClosing()&&this.playVideo()}onRefresh(t,e){e.slides.forEach((t=>{t.el&&(this.resizeIframe(t),this.setAspectRatio(t))}))}onMessage(t){try{let e=JSON.parse(t.data);if("https://player.vimeo.com"===t.origin){if("ready"===e.event)for(let e of Array.from(document.getElementsByClassName("fancybox__iframe")))e instanceof HTMLIFrameElement&&e.contentWindow===t.source&&(e.dataset.ready="true")}else if(t.origin.match(/^https:\/\/(www.)?youtube(-nocookie)?.com$/)&&"onReady"===e.event){const t=document.getElementById(e.id);t&&(t.dataset.ready="true")}}catch(t){}}loadAjaxContent(t){const e=this.instance.optionFor(t,"src")||"";this.instance.showLoading(t);const n=this.instance,i=new XMLHttpRequest;n.showLoading(t),i.onreadystatechange=function(){i.readyState===XMLHttpRequest.DONE&&n.state===at.Ready&&(n.hideLoading(t),200===i.status?n.setContent(t,i.responseText):n.setError(t,404===i.status?"{{AJAX_NOT_FOUND}}":"{{AJAX_FORBIDDEN}}"))};const o=t.ajax||null;i.open(o?"POST":"GET",e+""),i.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),i.setRequestHeader("X-Requested-With","XMLHttpRequest"),i.send(o),t.xhr=i}setInlineContent(t){let e=null;if(C(t.src))e=t.src;else if("string"==typeof t.src){const n=t.src.split("#",2).pop();e=n?document.getElementById(n):null}if(e){if("clone"===t.type||e.closest(".fancybox__slide")){e=e.cloneNode(!0);const n=e.dataset.animationName;n&&(e.classList.remove(n),delete e.dataset.animationName);let i=e.getAttribute("id");i=i?`${i}--clone`:`clone-${this.instance.id}-${t.index}`,e.setAttribute("id",i)}else if(e.parentNode){const n=document.createElement("div");n.classList.add("fancybox-placeholder"),e.parentNode.insertBefore(n,e),t.placeholderEl=n}this.instance.setContent(t,e)}else this.instance.setError(t,"{{ELEMENT_NOT_FOUND}}")}setIframeContent(t){const{src:e,el:n}=t;if(!e||"string"!=typeof e||!n)return;n.classList.add("is-loading");const i=this.instance,o=document.createElement("iframe");o.className="fancybox__iframe",o.setAttribute("id",`fancybox__iframe_${i.id}_${t.index}`);for(const[e,n]of Object.entries(this.optionFor(t,"iframeAttr")||{}))o.setAttribute(e,n);o.onerror=()=>{i.setError(t,"{{IFRAME_ERROR}}")},t.iframeEl=o;const s=this.optionFor(t,"preload");if("iframe"!==t.type||!1===s)return o.setAttribute("src",t.src+""),i.setContent(t,o,!1),this.resizeIframe(t),void i.revealContent(t);i.showLoading(t),o.onload=()=>{if(!o.src.length)return;const e="true"!==o.dataset.ready;o.dataset.ready="true",this.resizeIframe(t),e?i.revealContent(t):i.hideLoading(t)},o.setAttribute("src",e),i.setContent(t,o,!1)}resizeIframe(t){const{type:e,iframeEl:n}=t;if(e===Tt||e===Ct)return;const i=null==n?void 0:n.parentElement;if(!n||!i)return;let o=t.autoSize;void 0===o&&(o=this.optionFor(t,"autoSize"));let s=t.width||0,r=t.height||0;s&&r&&(o=!1);const a=i&&i.style;if(!1!==t.preload&&!1!==o&&a)try{const t=window.getComputedStyle(i),e=parseFloat(t.paddingLeft)+parseFloat(t.paddingRight),o=parseFloat(t.paddingTop)+parseFloat(t.paddingBottom),l=n.contentWindow;if(l){const t=l.document,n=t.getElementsByTagName(wt)[0],i=t.body;a.width="",i.style.overflow="hidden",s=s||n.scrollWidth+e,a.width=`${s}px`,i.style.overflow="",a.flex="0 0 auto",a.height=`${i.scrollHeight}px`,r=n.scrollHeight+o}}catch(t){}if(s||r){const t={flex:"0 1 auto",width:"",height:""};s&&"auto"!==s&&(t.width=`${s}px`),r&&"auto"!==r&&(t.height=`${r}px`),Object.assign(a,t)}}playVideo(){const t=this.instance.getSlide();if(!t)return;const{el:e}=t;if(!e||!e.offsetParent)return;if(!this.optionFor(t,"videoAutoplay"))return;if(t.type===_t)try{const t=e.querySelector("video");if(t){const e=t.play();void 0!==e&&e.then((()=>{})).catch((e=>{t.muted=!0,t.play()}))}}catch(t){}if(t.type!==Tt&&t.type!==Ct)return;const n=()=>{if(t.iframeEl&&t.iframeEl.contentWindow){let e;if("true"===t.iframeEl.dataset.ready)return e=t.type===Tt?{event:"command",func:"playVideo"}:{method:"play",value:"true"},e&&t.iframeEl.contentWindow.postMessage(JSON.stringify(e),"*"),void(t.poller=void 0);t.type===Tt&&(e={event:"listening",id:t.iframeEl.getAttribute("id")},t.iframeEl.contentWindow.postMessage(JSON.stringify(e),"*"))}t.poller=setTimeout(n,250)};n()}processType(t){if(t.html)return t.type=wt,t.src=t.html,void(t.html="");const e=this.instance.optionFor(t,"src","");if(!e||"string"!=typeof e)return;let n=t.type,i=null;if(i=e.match(/(youtube\.com|youtu\.be|youtube\-nocookie\.com)\/(?:watch\?(?:.*&)?v=|v\/|u\/|shorts\/|embed\/?)?(videoseries\?list=(?:.*)|[\w-]{11}|\?listType=(?:.*)&list=(?:.*))(?:.*)/i)){const o=this.optionFor(t,Tt),{nocookie:s}=o,r=function(t,e){var n={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.indexOf(i)<0&&(n[i]=t[i]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(i=Object.getOwnPropertySymbols(t);o<i.length;o++)e.indexOf(i[o])<0&&Object.prototype.propertyIsEnumerable.call(t,i[o])&&(n[i[o]]=t[i[o]])}return n}(o,["nocookie"]),a=`www.youtube${s?"-nocookie":""}.com`,l=Pt(e,r),c=encodeURIComponent(i[2]);t.videoId=c,t.src=`https://${a}/embed/${c}?${l}`,t.thumbSrc=t.thumbSrc||`https://i.ytimg.com/vi/${c}/mqdefault.jpg`,n=Tt}else if(i=e.match(/^.+vimeo.com\/(?:\/)?([\d]+)((\/|\?h=)([a-z0-9]+))?(.*)?/)){const o=Pt(e,this.optionFor(t,Ct)),s=encodeURIComponent(i[1]),r=i[4]||"";t.videoId=s,t.src=`https://player.vimeo.com/video/${s}?${r?`h=${r}${o?"&":""}`:""}${o}`,n=Ct}if(!n&&t.triggerEl){const e=t.triggerEl.dataset.type;kt.includes(e)&&(n=e)}n||"string"==typeof e&&("#"===e.charAt(0)?n="inline":(i=e.match(/\.(mp4|mov|ogv|webm)((\?|#).*)?$/i))?(n=_t,t.videoFormat=t.videoFormat||"video/"+("ogv"===i[1]?"ogg":i[1])):e.match(/(^data:image\/[a-z0-9+\/=]*,)|(\.(jp(e|g|eg)|gif|png|bmp|webp|svg|ico)((\?|#).*)?$)/i)?n=Et:e.match(/\.(pdf)((\?|#).*)?$/i)&&(n="pdf")),(i=e.match(/(?:maps\.)?google\.([a-z]{2,3}(?:\.[a-z]{2})?)\/(?:(?:(?:maps\/(?:place\/(?:.*)\/)?\@(.*),(\d+.?\d+?)z))|(?:\?ll=))(.*)?/i))?(t.src=`https://maps.google.${i[1]}/?ll=${(i[2]?i[2]+"&z="+Math.floor(parseFloat(i[3]))+(i[4]?i[4].replace(/^\//,"&"):""):i[4]+"").replace(/\?/,"&")}&output=${i[4]&&i[4].indexOf("layer=c")>0?"svembed":"embed"}`,n=St):(i=e.match(/(?:maps\.)?google\.([a-z]{2,3}(?:\.[a-z]{2})?)\/(?:maps\/search\/)(.*)/i))&&(t.src=`https://maps.google.${i[1]}/maps?q=${i[2].replace("query=","q=").replace("api=1","")}&output=embed`,n=St),n=n||this.instance.option("defaultType"),t.type=n,n===Et&&(t.thumbSrc=t.thumbSrc||t.src)}setContent(t){const e=this.instance.optionFor(t,"src")||"";if(t&&t.type&&e){switch(t.type){case wt:this.instance.setContent(t,e);break;case _t:const n=this.option("videoTpl");n&&this.instance.setContent(t,n.replace(/\{\{src\}\}/gi,e+"").replace(/\{\{format\}\}/gi,this.optionFor(t,"videoFormat")||"").replace(/\{\{poster\}\}/gi,t.poster||t.thumbSrc||""));break;case"inline":case"clone":this.setInlineContent(t);break;case"ajax":this.loadAjaxContent(t);break;case"pdf":case St:case Tt:case Ct:t.preload=!1;case"iframe":this.setIframeContent(t)}this.setAspectRatio(t)}}setAspectRatio(t){const e=t.contentEl;if(!(t.el&&e&&t.type&&[Tt,Ct,_t].includes(t.type)))return;let n,i=t.width||"auto",o=t.height||"auto";if("auto"===i||"auto"===o){n=this.optionFor(t,"videoRatio");const e=(n+"").match(/(\d+)\s*\/\s?(\d+)/);n=e&&e.length>2?parseFloat(e[1])/parseFloat(e[2]):parseFloat(n+"")}else i&&o&&(n=i/o);if(!n)return;e.style.aspectRatio="",e.style.width="",e.style.height="",e.offsetHeight;const s=e.getBoundingClientRect(),r=s.width||1,a=s.height||1;e.style.aspectRatio=n+"",n<r/a?(o="auto"===o?a:Math.min(a,o),e.style.width="auto",e.style.height=`${o}px`):(i="auto"===i?r:Math.min(r,i),e.style.width=`${i}px`,e.style.height="auto")}attach(){const t=this,e=t.instance;e.on("Carousel.beforeInitSlide",t.onBeforeInitSlide),e.on("Carousel.createSlide",t.onCreateSlide),e.on("Carousel.selectSlide",t.onSelectSlide),e.on("Carousel.unselectSlide",t.onUnselectSlide),e.on("Carousel.Panzoom.refresh",t.onRefresh),e.on("done",t.onDone),e.on("clearContent",t.onClearContent),window.addEventListener("message",t.onMessage)}detach(){const t=this,e=t.instance;e.off("Carousel.beforeInitSlide",t.onBeforeInitSlide),e.off("Carousel.createSlide",t.onCreateSlide),e.off("Carousel.selectSlide",t.onSelectSlide),e.off("Carousel.unselectSlide",t.onUnselectSlide),e.off("Carousel.Panzoom.refresh",t.onRefresh),e.off("done",t.onDone),e.off("clearContent",t.onClearContent),window.removeEventListener("message",t.onMessage)}}Object.defineProperty(Ot,"defaults",{enumerable:!0,configurable:!0,writable:!0,value:{ajax:null,autoSize:!0,iframeAttr:{allow:"autoplay; fullscreen",scrolling:"auto"},preload:!0,videoAutoplay:!0,videoRatio:16/9,videoTpl:'<video class="fancybox__html5video" playsinline controls controlsList="nodownload" poster="{{poster}}">\n  <source src="{{src}}" type="{{format}}" />Sorry, your browser doesn\'t support embedded videos.</video>',videoFormat:"",vimeo:{byline:1,color:"00adef",controls:1,dnt:1,muted:0},youtube:{controls:1,enablejsapi:1,nocookie:1,rel:0,fs:1}}});const Mt="play",At="pause",Lt="ready";class zt extends q{constructor(){super(...arguments),Object.defineProperty(this,"state",{enumerable:!0,configurable:!0,writable:!0,value:Lt}),Object.defineProperty(this,"inHover",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"timer",{enumerable:!0,configurable:!0,writable:!0,value:null}),Object.defineProperty(this,"progressBar",{enumerable:!0,configurable:!0,writable:!0,value:null})}get isActive(){return this.state!==Lt}onReady(t){this.option("autoStart")&&(t.isInfinite||t.page<t.pages.length-1)&&this.start()}onChange(){this.removeProgressBar(),this.pause()}onSettle(){this.resume()}onVisibilityChange(){"visible"===document.visibilityState?this.resume():this.pause()}onMouseEnter(){this.inHover=!0,this.pause()}onMouseLeave(){var t;this.inHover=!1,(null===(t=this.instance.panzoom)||void 0===t?void 0:t.isResting)&&this.resume()}onTimerEnd(){const t=this.instance;"play"===this.state&&(t.isInfinite||t.page!==t.pages.length-1?t.slideNext():t.slideTo(0))}removeProgressBar(){this.progressBar&&(this.progressBar.remove(),this.progressBar=null)}createProgressBar(){var t;if(!this.option("showProgress"))return null;this.removeProgressBar();const e=this.instance,n=(null===(t=e.pages[e.page])||void 0===t?void 0:t.slides)||[];let i=this.option("progressParentEl");if(i||(i=(1===n.length?n[0].el:null)||e.viewport),!i)return null;const o=document.createElement("div");return P(o,"f-progress"),i.prepend(o),this.progressBar=o,o.offsetHeight,o}set(){const t=this,e=t.instance;if(e.pages.length<2)return;if(t.timer)return;const n=t.option("timeout");t.state=Mt,P(e.container,"has-autoplay");let i=t.createProgressBar();i&&(i.style.transitionDuration=`${n}ms`,i.style.transform="scaleX(1)"),t.timer=setTimeout((()=>{t.timer=null,t.inHover||t.onTimerEnd()}),n),t.emit("set")}clear(){const t=this;t.timer&&(clearTimeout(t.timer),t.timer=null),t.removeProgressBar()}start(){const t=this;if(t.set(),t.state!==Lt){if(t.option("pauseOnHover")){const e=t.instance.container;e.addEventListener("mouseenter",t.onMouseEnter,!1),e.addEventListener("mouseleave",t.onMouseLeave,!1)}document.addEventListener("visibilitychange",t.onVisibilityChange,!1),t.emit("start")}}stop(){const t=this,e=t.state,n=t.instance.container;t.clear(),t.state=Lt,n.removeEventListener("mouseenter",t.onMouseEnter,!1),n.removeEventListener("mouseleave",t.onMouseLeave,!1),document.removeEventListener("visibilitychange",t.onVisibilityChange,!1),_(n,"has-autoplay"),e!==Lt&&t.emit("stop")}pause(){const t=this;t.state===Mt&&(t.state=At,t.clear(),t.emit(At))}resume(){const t=this,e=t.instance;if(e.isInfinite||e.page!==e.pages.length-1)if(t.state!==Mt){if(t.state===At&&!t.inHover){const e=new Event("resume",{bubbles:!0,cancelable:!0});t.emit("resume",e),e.defaultPrevented||t.set()}}else t.set();else t.stop()}toggle(){this.state===Mt||this.state===At?this.stop():this.start()}attach(){const t=this,e=t.instance;e.on("ready",t.onReady),e.on("Panzoom.startAnimation",t.onChange),e.on("Panzoom.endAnimation",t.onSettle),e.on("Panzoom.touchMove",t.onChange)}detach(){const t=this,e=t.instance;e.off("ready",t.onReady),e.off("Panzoom.startAnimation",t.onChange),e.off("Panzoom.endAnimation",t.onSettle),e.off("Panzoom.touchMove",t.onChange),t.stop()}}Object.defineProperty(zt,"defaults",{enumerable:!0,configurable:!0,writable:!0,value:{autoStart:!0,pauseOnHover:!0,progressParentEl:null,showProgress:!0,timeout:3e3}});class Dt extends q{constructor(){super(...arguments),Object.defineProperty(this,"ref",{enumerable:!0,configurable:!0,writable:!0,value:null})}onPrepare(t){const e=t.carousel;if(!e)return;const n=t.container;n&&(e.options.Autoplay=g({autoStart:!1},this.option("Autoplay")||{},{pauseOnHover:!1,timeout:this.option("timeout"),progressParentEl:()=>this.option("progressParentEl")||null,on:{start:()=>{t.emit("startSlideshow")},set:e=>{var i;n.classList.add("has-slideshow"),(null===(i=t.getSlide())||void 0===i?void 0:i.state)!==lt.Ready&&e.pause()},stop:()=>{n.classList.remove("has-slideshow"),t.isCompact||t.endIdle(),t.emit("endSlideshow")},resume:(e,n)=>{var i,o,s;!n||!n.cancelable||(null===(i=t.getSlide())||void 0===i?void 0:i.state)===lt.Ready&&(null===(s=null===(o=t.carousel)||void 0===o?void 0:o.panzoom)||void 0===s?void 0:s.isResting)||n.preventDefault()}}}),e.attachPlugins({Autoplay:zt}),this.ref=e.plugins.Autoplay)}onReady(t){const e=t.carousel,n=this.ref;n&&e&&this.option("playOnStart")&&(e.isInfinite||e.page<e.pages.length-1)&&n.start()}onDone(t,e){const n=this.ref,i=t.carousel;if(!n||!i)return;const o=e.panzoom;o&&o.on("startAnimation",(()=>{t.isCurrentSlide(e)&&n.stop()})),t.isCurrentSlide(e)&&n.resume()}onKeydown(t,e){var n;const i=this.ref;i&&e===this.option("key")&&"BUTTON"!==(null===(n=document.activeElement)||void 0===n?void 0:n.nodeName)&&i.toggle()}attach(){const t=this,e=t.instance;e.on("Carousel.init",t.onPrepare),e.on("Carousel.ready",t.onReady),e.on("done",t.onDone),e.on("keydown",t.onKeydown)}detach(){const t=this,e=t.instance;e.off("Carousel.init",t.onPrepare),e.off("Carousel.ready",t.onReady),e.off("done",t.onDone),e.off("keydown",t.onKeydown)}}Object.defineProperty(Dt,"defaults",{enumerable:!0,configurable:!0,writable:!0,value:{key:" ",playOnStart:!1,progressParentEl:t=>{var e;return(null===(e=t.instance.container)||void 0===e?void 0:e.querySelector(".fancybox__toolbar [data-fancybox-toggle-slideshow]"))||t.instance.container},timeout:3e3}});const jt={classes:{container:"f-thumbs f-carousel__thumbs",viewport:"f-thumbs__viewport",track:"f-thumbs__track",slide:"f-thumbs__slide",isResting:"is-resting",isSelected:"is-selected",isLoading:"is-loading",hasThumbs:"has-thumbs"},minCount:2,parentEl:null,thumbTpl:'<button class="f-thumbs__slide__button" tabindex="0" type="button" aria-label="{{GOTO}}" data-carousel-index="%i"><img class="f-thumbs__slide__img" data-lazy-src="{{%s}}" alt="" /></button>',type:"modern"};var It;!function(t){t[t.Init=0]="Init",t[t.Ready=1]="Ready",t[t.Hidden=2]="Hidden"}(It||(It={}));const Rt="isResting",Ft="thumbWidth",Nt="thumbHeight",Ht="thumbClipWidth";let Bt=class extends q{constructor(){super(...arguments),Object.defineProperty(this,"type",{enumerable:!0,configurable:!0,writable:!0,value:"modern"}),Object.defineProperty(this,"container",{enumerable:!0,configurable:!0,writable:!0,value:null}),Object.defineProperty(this,"track",{enumerable:!0,configurable:!0,writable:!0,value:null}),Object.defineProperty(this,"carousel",{enumerable:!0,configurable:!0,writable:!0,value:null}),Object.defineProperty(this,"thumbWidth",{enumerable:!0,configurable:!0,writable:!0,value:0}),Object.defineProperty(this,"thumbClipWidth",{enumerable:!0,configurable:!0,writable:!0,value:0}),Object.defineProperty(this,"thumbHeight",{enumerable:!0,configurable:!0,writable:!0,value:0}),Object.defineProperty(this,"thumbGap",{enumerable:!0,configurable:!0,writable:!0,value:0}),Object.defineProperty(this,"thumbExtraGap",{enumerable:!0,configurable:!0,writable:!0,value:0}),Object.defineProperty(this,"state",{enumerable:!0,configurable:!0,writable:!0,value:It.Init})}get isModern(){return"modern"===this.type}onInitSlide(t,e){const n=e.el?e.el.dataset:void 0;n&&(e.thumbSrc=n.thumbSrc||e.thumbSrc||"",e[Ht]=parseFloat(n[Ht]||"")||e[Ht]||0,e[Nt]=parseFloat(n.thumbHeight||"")||e[Nt]||0),this.addSlide(e)}onInitSlides(){this.build()}onChange(){var t;if(!this.isModern)return;const e=this.container,n=this.instance,i=n.panzoom,o=this.carousel,s=o?o.panzoom:null,r=n.page;if(i&&o&&s){if(i.isDragging){_(e,this.cn(Rt));let i=(null===(t=o.pages[r])||void 0===t?void 0:t.pos)||0;i+=n.getProgress(r)*(this[Ht]+this.thumbGap);let a=s.getBounds();-1*i>a.x.min&&-1*i<a.x.max&&s.panTo({x:-1*i,friction:.12})}else l(e,this.cn(Rt),i.isResting);this.shiftModern()}}onRefresh(){this.updateProps();for(const t of this.instance.slides||[])this.resizeModernSlide(t);this.shiftModern()}isDisabled(){const t=this.option("minCount")||0;if(t){const e=this.instance;let n=0;for(const t of e.slides||[])t.thumbSrc&&n++;if(n<t)return!0}const e=this.option("type");return["modern","classic"].indexOf(e)<0}getThumb(t){const e=this.option("thumbTpl")||"";return{html:this.instance.localize(e,[["%i",t.index],["%d",t.index+1],["%s",t.thumbSrc||"data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"]])}}addSlide(t){const e=this.carousel;e&&e.addSlide(t.index,this.getThumb(t))}getSlides(){const t=[];for(const e of this.instance.slides||[])t.push(this.getThumb(e));return t}resizeModernSlide(t){this.isModern&&(t[Ft]=t[Ht]&&t[Nt]?Math.round(this[Nt]*(t[Ht]/t[Nt])):this[Ft])}updateProps(){const t=this.container;if(!t)return;const e=e=>parseFloat(getComputedStyle(t).getPropertyValue("--f-thumb-"+e))||0;this.thumbGap=e("gap"),this.thumbExtraGap=e("extra-gap"),this[Ft]=e("width")||40,this[Ht]=e("clip-width")||40,this[Nt]=e("height")||40}build(){const t=this;if(t.state!==It.Init)return;if(t.isDisabled())return void t.emit("disabled");const e=t.instance,n=e.container,i=t.getSlides(),o=t.option("type");t.type=o;const s=t.option("parentEl"),r=t.cn("container"),a=t.cn("track");let l=null==s?void 0:s.querySelector("."+r);l||(l=document.createElement("div"),P(l,r),s?s.appendChild(l):n.after(l)),P(l,`is-${o}`),P(n,t.cn("hasThumbs")),t.container=l,t.updateProps();let c=l.querySelector("."+a);c||(c=document.createElement("div"),P(c,t.cn("track")),l.appendChild(c)),t.track=c;const u=g({},{track:c,infinite:!1,center:!0,fill:"classic"===o,dragFree:!0,slidesPerPage:1,transition:!1,preload:.25,friction:.12,Panzoom:{maxVelocity:0},Dots:!1,Navigation:!1,classes:{container:"f-thumbs",viewport:"f-thumbs__viewport",track:"f-thumbs__track",slide:"f-thumbs__slide"}},t.option("Carousel")||{},{Sync:{target:e},slides:i}),d=new e.constructor(l,u);d.on("createSlide",((e,n)=>{t.setProps(n.index),t.emit("createSlide",n,n.el)})),d.on("ready",(()=>{t.shiftModern(),t.emit("ready")})),d.on("refresh",(()=>{t.shiftModern()})),d.on("Panzoom.click",((e,n,i)=>{t.onClick(i)})),t.carousel=d,t.state=It.Ready}onClick(t){t.preventDefault(),t.stopPropagation();const e=this.instance,{pages:n,page:i}=e,o=t=>{if(t){const e=t.closest("[data-carousel-index]");if(e)return[parseInt(e.dataset.carouselIndex||"",10)||0,e]}return[-1,void 0]},s=(t,e)=>{const n=document.elementFromPoint(t,e);return n?o(n):[-1,void 0]};let[r,a]=o(t.target);if(r>-1)return;const l=this[Ht],c=t.clientX,u=t.clientY;let[d,h]=s(c-l,u),[f,p]=s(c+l,u);h&&p?(r=Math.abs(c-h.getBoundingClientRect().right)<Math.abs(c-p.getBoundingClientRect().left)?d:f,r===i&&(r=r===d?f:d)):h?r=d:p&&(r=f),r>-1&&n[r]&&e.slideTo(r)}getShift(t){var e;const n=this,{instance:i}=n,o=n.carousel;if(!i||!o)return 0;const s=n[Ft],r=n[Ht],a=n.thumbGap,l=n.thumbExtraGap;if(!(null===(e=o.slides[t])||void 0===e?void 0:e.el))return 0;const c=.5*(s-r),u=i.pages.length-1;let d=i.getProgress(0),h=i.getProgress(u),f=i.getProgress(t,!1,!0),p=0,g=c+l+a;const m=d<0&&d>-1,v=h>0&&h<1;return 0===t?(p=g*Math.abs(d),v&&1===d&&(p-=g*Math.abs(h))):t===u?(p=g*Math.abs(h)*-1,m&&-1===h&&(p+=g*Math.abs(d))):m||v?(p=-1*g,p+=g*Math.abs(d),p+=g*(1-Math.abs(h))):p=g*f,p}setProps(t){var e;const n=this;if(!n.isModern)return;const{instance:o}=n,s=n.carousel;if(o&&s){const r=null===(e=s.slides[t])||void 0===e?void 0:e.el;if(r&&r.childNodes.length){let e=i(1-Math.abs(o.getProgress(t))),s=i(n.getShift(t));r.style.setProperty("--progress",e?e+"":""),r.style.setProperty("--shift",s+"")}}}shiftModern(){const t=this;if(!t.isModern)return;const{instance:e,track:n}=t,i=e.panzoom,o=t.carousel;if(!(e&&n&&i&&o))return;if(i.state===y.Init||i.state===y.Destroy)return;for(const n of e.slides)t.setProps(n.index);let s=(t[Ht]+t.thumbGap)*(o.slides.length||0);n.style.setProperty("--width",s+"")}cleanup(){const t=this;t.carousel&&t.carousel.destroy(),t.carousel=null,t.container&&t.container.remove(),t.container=null,t.track&&t.track.remove(),t.track=null,t.state=It.Init,_(t.instance.container,t.cn("hasThumbs"))}attach(){const t=this,e=t.instance;e.on("initSlide",t.onInitSlide),e.state===N.Init?e.on("initSlides",t.onInitSlides):t.onInitSlides(),e.on(["change","Panzoom.afterTransform"],t.onChange),e.on("Panzoom.refresh",t.onRefresh)}detach(){const t=this,e=t.instance;e.off("initSlide",t.onInitSlide),e.off("initSlides",t.onInitSlides),e.off(["change","Panzoom.afterTransform"],t.onChange),e.off("Panzoom.refresh",t.onRefresh),t.cleanup()}};Object.defineProperty(Bt,"defaults",{enumerable:!0,configurable:!0,writable:!0,value:jt});const qt=Object.assign(Object.assign({},jt),{key:"t",showOnStart:!0,parentEl:null}),$t="is-masked",Wt="aria-hidden";class Xt extends q{constructor(){super(...arguments),Object.defineProperty(this,"ref",{enumerable:!0,configurable:!0,writable:!0,value:null}),Object.defineProperty(this,"hidden",{enumerable:!0,configurable:!0,writable:!0,value:!1})}get isEnabled(){const t=this.ref;return t&&!t.isDisabled()}get isHidden(){return this.hidden}onClick(t,e){e.stopPropagation()}onCreateSlide(t,e){var n,i,o;const s=(null===(o=null===(i=null===(n=this.instance)||void 0===n?void 0:n.carousel)||void 0===i?void 0:i.slides[e.index])||void 0===o?void 0:o.type)||"",r=e.el;if(r&&s){let t=`for-${s}`;["video","youtube","vimeo","html5video"].includes(s)&&(t+=" for-video"),P(r,t)}}onInit(){var t;const e=this,n=e.instance,i=n.carousel;if(e.ref||!i)return;const o=e.option("parentEl")||n.footer||n.container;if(!o)return;const s=g({},e.options,{parentEl:o,classes:{container:"f-thumbs fancybox__thumbs"},Carousel:{Sync:{friction:n.option("Carousel.friction")||0}},on:{ready:t=>{const n=t.container;n&&this.hidden&&(e.refresh(),n.style.transition="none",e.hide(),n.offsetHeight,queueMicrotask((()=>{n.style.transition="",e.show()})))}}});s.Carousel=s.Carousel||{},s.Carousel.on=g((null===(t=e.options.Carousel)||void 0===t?void 0:t.on)||{},{click:this.onClick,createSlide:this.onCreateSlide}),i.options.Thumbs=s,i.attachPlugins({Thumbs:Bt}),e.ref=i.plugins.Thumbs,e.option("showOnStart")||(e.ref.state=It.Hidden,e.hidden=!0)}onResize(){var t;const e=null===(t=this.ref)||void 0===t?void 0:t.container;e&&(e.style.maxHeight="")}onKeydown(t,e){const n=this.option("key");n&&n===e&&this.toggle()}toggle(){const t=this.ref;if(t&&!t.isDisabled())return t.state===It.Hidden?(t.state=It.Init,void t.build()):void(this.hidden?this.show():this.hide())}show(){const t=this.ref;if(!t||t.isDisabled())return;const e=t.container;e&&(this.refresh(),e.offsetHeight,e.removeAttribute(Wt),e.classList.remove($t),this.hidden=!1)}hide(){const t=this.ref,e=t&&t.container;e&&(this.refresh(),e.offsetHeight,e.classList.add($t),e.setAttribute(Wt,"true")),this.hidden=!0}refresh(){const t=this.ref;if(!t||!t.state)return;const e=t.container,n=(null==e?void 0:e.firstChild)||null;e&&n&&n.childNodes.length&&(e.style.maxHeight=`${n.getBoundingClientRect().height}px`)}attach(){const t=this,e=t.instance;e.state===at.Init?e.on("Carousel.init",t.onInit):t.onInit(),e.on("resize",t.onResize),e.on("keydown",t.onKeydown)}detach(){var t;const e=this,n=e.instance;n.off("Carousel.init",e.onInit),n.off("resize",e.onResize),n.off("keydown",e.onKeydown),null===(t=n.carousel)||void 0===t||t.detachPlugins(["Thumbs"]),e.ref=null}}Object.defineProperty(Xt,"defaults",{enumerable:!0,configurable:!0,writable:!0,value:qt});const Yt={panLeft:{icon:'<svg><path d="M5 12h14M5 12l6 6M5 12l6-6"/></svg>',change:{panX:-100}},panRight:{icon:'<svg><path d="M5 12h14M13 18l6-6M13 6l6 6"/></svg>',change:{panX:100}},panUp:{icon:'<svg><path d="M12 5v14M18 11l-6-6M6 11l6-6"/></svg>',change:{panY:-100}},panDown:{icon:'<svg><path d="M12 5v14M18 13l-6 6M6 13l6 6"/></svg>',change:{panY:100}},zoomIn:{icon:'<svg><circle cx="11" cy="11" r="7.5"/><path d="m21 21-4.35-4.35M11 8v6M8 11h6"/></svg>',action:"zoomIn"},zoomOut:{icon:'<svg><circle cx="11" cy="11" r="7.5"/><path d="m21 21-4.35-4.35M8 11h6"/></svg>',action:"zoomOut"},toggle1to1:{icon:'<svg><path d="M3.51 3.07c5.74.02 11.48-.02 17.22.02 1.37.1 2.34 1.64 2.18 3.13 0 4.08.02 8.16 0 12.23-.1 1.54-1.47 2.64-2.79 2.46-5.61-.01-11.24.02-16.86-.01-1.36-.12-2.33-1.65-2.17-3.14 0-4.07-.02-8.16 0-12.23.1-1.36 1.22-2.48 2.42-2.46Z"/><path d="M5.65 8.54h1.49v6.92m8.94-6.92h1.49v6.92M11.5 9.4v.02m0 5.18v0"/></svg>',action:"toggleZoom"},toggleZoom:{icon:'<svg><g><line x1="11" y1="8" x2="11" y2="14"></line></g><circle cx="11" cy="11" r="7.5"/><path d="m21 21-4.35-4.35M8 11h6"/></svg>',action:"toggleZoom"},iterateZoom:{icon:'<svg><g><line x1="11" y1="8" x2="11" y2="14"></line></g><circle cx="11" cy="11" r="7.5"/><path d="m21 21-4.35-4.35M8 11h6"/></svg>',action:"iterateZoom"},rotateCCW:{icon:'<svg><path d="M15 4.55a8 8 0 0 0-6 14.9M9 15v5H4M18.37 7.16v.01M13 19.94v.01M16.84 18.37v.01M19.37 15.1v.01M19.94 11v.01"/></svg>',action:"rotateCCW"},rotateCW:{icon:'<svg><path d="M9 4.55a8 8 0 0 1 6 14.9M15 15v5h5M5.63 7.16v.01M4.06 11v.01M4.63 15.1v.01M7.16 18.37v.01M11 19.94v.01"/></svg>',action:"rotateCW"},flipX:{icon:'<svg style="stroke-width: 1.3"><path d="M12 3v18M16 7v10h5L16 7M8 7v10H3L8 7"/></svg>',action:"flipX"},flipY:{icon:'<svg style="stroke-width: 1.3"><path d="M3 12h18M7 16h10L7 21v-5M7 8h10L7 3v5"/></svg>',action:"flipY"},fitX:{icon:'<svg><path d="M4 12V6a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v6M10 18H3M21 18h-7M6 15l-3 3 3 3M18 15l3 3-3 3"/></svg>',action:"fitX"},fitY:{icon:'<svg><path d="M12 20H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h6M18 14v7M18 3v7M15 18l3 3 3-3M15 6l3-3 3 3"/></svg>',action:"fitY"},reset:{icon:'<svg><path d="M20 11A8.1 8.1 0 0 0 4.5 9M4 5v4h4M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4"/></svg>',action:"reset"},toggleFS:{icon:'<svg><g><path d="M14.5 9.5 21 3m0 0h-6m6 0v6M3 21l6.5-6.5M3 21v-6m0 6h6"/></g><g><path d="m14 10 7-7m-7 7h6m-6 0V4M3 21l7-7m0 0v6m0-6H4"/></g></svg>',action:"toggleFS"}};var Ut;!function(t){t[t.Init=0]="Init",t[t.Ready=1]="Ready",t[t.Disabled=2]="Disabled"}(Ut||(Ut={}));const Vt={tabindex:"-1",width:"24",height:"24",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},Zt="has-toolbar",Gt="fancybox__toolbar";class Kt extends q{constructor(){super(...arguments),Object.defineProperty(this,"state",{enumerable:!0,configurable:!0,writable:!0,value:Ut.Init}),Object.defineProperty(this,"container",{enumerable:!0,configurable:!0,writable:!0,value:null})}onReady(t){var e;if(!t.carousel)return;let n=this.option("display"),i=this.option("absolute"),o=this.option("enabled");if("auto"===o){const t=this.instance.carousel;let e=0;if(t)for(const n of t.slides)(n.panzoom||"image"===n.type)&&e++;e||(o=!1)}o||(n=void 0);let s=0;const r={left:[],middle:[],right:[]};if(n)for(const t of["left","middle","right"])for(const i of n[t]){const n=this.createEl(i);n&&(null===(e=r[t])||void 0===e||e.push(n),s++)}let a=null;if(s&&(a=this.createContainer()),a){for(const[t,e]of Object.entries(r)){const n=document.createElement("div");P(n,Gt+"__column is-"+t);for(const t of e)n.appendChild(t);"auto"!==i||"middle"!==t||e.length||(i=!0),a.appendChild(n)}!0===i&&P(a,"is-absolute"),this.state=Ut.Ready,this.onRefresh()}else this.state=Ut.Disabled}onClick(t){var e,n;const i=this.instance,o=i.getSlide(),s=null==o?void 0:o.panzoom,r=t.target,a=r&&C(r)?r.dataset:null;if(!a)return;if(void 0!==a.fancyboxToggleThumbs)return t.preventDefault(),t.stopPropagation(),void(null===(e=i.plugins.Thumbs)||void 0===e||e.toggle());if(void 0!==a.fancyboxToggleFullscreen)return t.preventDefault(),t.stopPropagation(),void this.instance.toggleFullscreen();if(void 0!==a.fancyboxToggleSlideshow){t.preventDefault(),t.stopPropagation();const e=null===(n=i.carousel)||void 0===n?void 0:n.plugins.Autoplay;let o=e.isActive;return s&&"mousemove"===s.panMode&&!o&&s.reset(),void(o?e.stop():e.start())}const l=a.panzoomAction,c=a.panzoomChange;if((c||l)&&(t.preventDefault(),t.stopPropagation()),c){let e={};try{e=JSON.parse(c)}catch(t){}s&&s.applyChange(e)}else l&&s&&s[l]&&s[l]()}onChange(){this.onRefresh()}onRefresh(){if(this.instance.isClosing())return;const t=this.container;if(!t)return;const e=this.instance.getSlide();if(!e||e.state!==lt.Ready)return;const n=e&&!e.error&&e.panzoom;for(const e of t.querySelectorAll("[data-panzoom-action]"))n?(e.removeAttribute("disabled"),e.removeAttribute("tabindex")):(e.setAttribute("disabled",""),e.setAttribute("tabindex","-1"));let i=n&&n.canZoomIn(),o=n&&n.canZoomOut();for(const e of t.querySelectorAll('[data-panzoom-action="zoomIn"]'))i?(e.removeAttribute("disabled"),e.removeAttribute("tabindex")):(e.setAttribute("disabled",""),e.setAttribute("tabindex","-1"));for(const e of t.querySelectorAll('[data-panzoom-action="zoomOut"]'))o?(e.removeAttribute("disabled"),e.removeAttribute("tabindex")):(e.setAttribute("disabled",""),e.setAttribute("tabindex","-1"));for(const e of t.querySelectorAll('[data-panzoom-action="toggleZoom"],[data-panzoom-action="iterateZoom"]')){o||i?(e.removeAttribute("disabled"),e.removeAttribute("tabindex")):(e.setAttribute("disabled",""),e.setAttribute("tabindex","-1"));const t=e.querySelector("g");t&&(t.style.display=i?"":"none")}}onDone(t,e){var n;null===(n=e.panzoom)||void 0===n||n.on("afterTransform",(()=>{this.instance.isCurrentSlide(e)&&this.onRefresh()})),this.instance.isCurrentSlide(e)&&this.onRefresh()}createContainer(){const t=this.instance.container;if(!t)return null;const e=this.option("parentEl")||t;let n=e.querySelector("."+Gt);return n||(n=document.createElement("div"),P(n,Gt),e.prepend(n)),n.addEventListener("click",this.onClick,{passive:!1,capture:!0}),t&&P(t,Zt),this.container=n,n}createEl(t){const e=this.instance,n=e.carousel;if(!n)return null;if("toggleFS"===t)return null;if("fullscreen"===t&&!st())return null;let i=null;const o=n.slides.length||0;let s=0,a=0;for(const t of n.slides)(t.panzoom||"image"===t.type)&&s++,("image"===t.type||t.downloadSrc)&&a++;if(o<2&&["infobar","prev","next"].includes(t))return i;if(void 0!==Yt[t]&&!s)return null;if("download"===t&&!a)return null;if("thumbs"===t){const t=e.plugins.Thumbs;if(!t||!t.isEnabled)return null}if("slideshow"===t&&(!n.plugins.Autoplay||o<2))return null;if(void 0!==Yt[t]){const e=Yt[t];i=document.createElement("button"),i.setAttribute("title",this.instance.localize(`{{${t.toUpperCase()}}}`)),P(i,"f-button"),e.action&&(i.dataset.panzoomAction=e.action),e.change&&(i.dataset.panzoomChange=JSON.stringify(e.change)),i.appendChild(r(this.instance.localize(e.icon)))}else{const e=(this.option("items")||[])[t];e&&(i=r(this.instance.localize(e.tpl)),"function"==typeof e.click&&i.addEventListener("click",(t=>{t.preventDefault(),t.stopPropagation(),"function"==typeof e.click&&e.click.call(this,this,t)})))}const l=null==i?void 0:i.querySelector("svg");if(l)for(const[t,e]of Object.entries(Vt))l.getAttribute(t)||l.setAttribute(t,String(e));return i}removeContainer(){const t=this.container;t&&t.remove(),this.container=null,this.state=Ut.Disabled;const e=this.instance.container;e&&_(e,Zt)}attach(){const t=this,e=t.instance;e.on("Carousel.initSlides",t.onReady),e.on("done",t.onDone),e.on(["reveal","Carousel.change"],t.onChange),t.onReady(t.instance)}detach(){const t=this,e=t.instance;e.off("Carousel.initSlides",t.onReady),e.off("done",t.onDone),e.off(["reveal","Carousel.change"],t.onChange),t.removeContainer()}}Object.defineProperty(Kt,"defaults",{enumerable:!0,configurable:!0,writable:!0,value:{absolute:"auto",display:{left:["infobar"],middle:[],right:["iterateZoom","slideshow","fullscreen","thumbs","close"]},enabled:"auto",items:{infobar:{tpl:'<div class="fancybox__infobar" tabindex="-1"><span data-fancybox-current-index></span>/<span data-fancybox-count></span></div>'},download:{tpl:'<a class="f-button" title="{{DOWNLOAD}}" data-fancybox-download href="javasript:;"><svg><path d="M4 17v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-2M7 11l5 5 5-5M12 4v12"/></svg></a>'},prev:{tpl:'<button class="f-button" title="{{PREV}}" data-fancybox-prev><svg><path d="m15 6-6 6 6 6"/></svg></button>'},next:{tpl:'<button class="f-button" title="{{NEXT}}" data-fancybox-next><svg><path d="m9 6 6 6-6 6"/></svg></button>'},slideshow:{tpl:'<button class="f-button" title="{{TOGGLE_SLIDESHOW}}" data-fancybox-toggle-slideshow><svg><g><path d="M8 4v16l13 -8z"></path></g><g><path d="M8 4v15M17 4v15"/></g></svg></button>'},fullscreen:{tpl:'<button class="f-button" title="{{TOGGLE_FULLSCREEN}}" data-fancybox-toggle-fullscreen><svg><g><path d="M4 8V6a2 2 0 0 1 2-2h2M4 16v2a2 2 0 0 0 2 2h2M16 4h2a2 2 0 0 1 2 2v2M16 20h2a2 2 0 0 0 2-2v-2"/></g><g><path d="M15 19v-2a2 2 0 0 1 2-2h2M15 5v2a2 2 0 0 0 2 2h2M5 15h2a2 2 0 0 1 2 2v2M5 9h2a2 2 0 0 0 2-2V5"/></g></svg></button>'},thumbs:{tpl:'<button class="f-button" title="{{TOGGLE_THUMBS}}" data-fancybox-toggle-thumbs><svg><circle cx="5.5" cy="5.5" r="1"/><circle cx="12" cy="5.5" r="1"/><circle cx="18.5" cy="5.5" r="1"/><circle cx="5.5" cy="12" r="1"/><circle cx="12" cy="12" r="1"/><circle cx="18.5" cy="12" r="1"/><circle cx="5.5" cy="18.5" r="1"/><circle cx="12" cy="18.5" r="1"/><circle cx="18.5" cy="18.5" r="1"/></svg></button>'},close:{tpl:'<button class="f-button" title="{{CLOSE}}" data-fancybox-close><svg><path d="m19.5 4.5-15 15M4.5 4.5l15 15"/></svg></button>'}},parentEl:null}});const Jt={Hash:class extends q{onReady(){ut=!1}onChange(t){ht&&clearTimeout(ht);const{hash:e}=ft(),{hash:n}=pt(),i=t.isOpeningSlide(t.getSlide());i&&(ct=n===e?"":n),e&&e!==n&&(ht=setTimeout((()=>{try{if(t.state===at.Ready){let t="replaceState";i&&!dt&&(t="pushState",dt=!0),window.history[t]({},document.title,window.location.pathname+window.location.search+e)}}catch(t){}}),300))}onClose(t){if(ht&&clearTimeout(ht),!ut&&dt)return dt=!1,ut=!1,void window.history.back();if(!ut)try{window.history.replaceState({},document.title,window.location.pathname+window.location.search+(ct||""))}catch(t){}}attach(){const t=this.instance;t.on("ready",this.onReady),t.on(["Carousel.ready","Carousel.change"],this.onChange),t.on("close",this.onClose)}detach(){const t=this.instance;t.off("ready",this.onReady),t.off(["Carousel.ready","Carousel.change"],this.onChange),t.off("close",this.onClose)}static parseURL(){return pt()}static startFromUrl(){gt()}static destroy(){window.removeEventListener("hashchange",vt,!1)}},Html:Ot,Images:xt,Slideshow:Dt,Thumbs:Xt,Toolbar:Kt},Qt="with-fancybox",te="hide-scrollbar",ee="--fancybox-scrollbar-compensate",ne="--fancybox-body-margin",ie="aria-hidden",oe="is-using-tab",se="is-animated",re="is-compact",ae="is-loading",le="is-opening",ce="has-caption",ue="disabled",de="tabindex",he="download",fe="href",pe="src",ge=t=>"string"==typeof t,me=function(){var t=window.getSelection();return!!t&&"Range"===t.type};let ve,be=null,ye=null,xe=0,we=0,Ee=0,Se=0;const Te=new Map;let Ce=0;class _e extends b{get isIdle(){return this.idle}get isCompact(){return this.option("compact")}constructor(t=[],e={},n={}){super(e),Object.defineProperty(this,"userSlides",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"userPlugins",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"idle",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"idleTimer",{enumerable:!0,configurable:!0,writable:!0,value:null}),Object.defineProperty(this,"clickTimer",{enumerable:!0,configurable:!0,writable:!0,value:null}),Object.defineProperty(this,"pwt",{enumerable:!0,configurable:!0,writable:!0,value:0}),Object.defineProperty(this,"ignoreFocusChange",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"startedFs",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"state",{enumerable:!0,configurable:!0,writable:!0,value:at.Init}),Object.defineProperty(this,"id",{enumerable:!0,configurable:!0,writable:!0,value:0}),Object.defineProperty(this,"container",{enumerable:!0,configurable:!0,writable:!0,value:null}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,writable:!0,value:null}),Object.defineProperty(this,"footer",{enumerable:!0,configurable:!0,writable:!0,value:null}),Object.defineProperty(this,"carousel",{enumerable:!0,configurable:!0,writable:!0,value:null}),Object.defineProperty(this,"lastFocus",{enumerable:!0,configurable:!0,writable:!0,value:null}),Object.defineProperty(this,"prevMouseMoveEvent",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),ve||(ve=st()),this.id=e.id||++Ce,Te.set(this.id,this),this.userSlides=t,this.userPlugins=n,queueMicrotask((()=>{this.init()}))}init(){if(this.state===at.Destroy)return;this.state=at.Init,this.attachPlugins(Object.assign(Object.assign({},_e.Plugins),this.userPlugins)),this.emit("init"),this.emit("attachPlugins"),!0===this.option("hideScrollbar")&&(()=>{if(!et)return;const t=document,e=t.body,n=t.documentElement;if(e.classList.contains(te))return;let i=window.innerWidth-n.getBoundingClientRect().width;const o=parseFloat(window.getComputedStyle(e).marginRight);i<0&&(i=0),n.style.setProperty(ee,`${i}px`),o&&e.style.setProperty(ne,`${o}px`),e.classList.add(te)})(),this.initLayout(),this.scale();const t=()=>{this.initCarousel(this.userSlides),this.state=at.Ready,this.attachEvents(),this.emit("ready"),setTimeout((()=>{this.container&&this.container.setAttribute(ie,"false")}),16)};this.option("Fullscreen.autoStart")&&ve&&!ve.isFullscreen()?ve.request().then((()=>{this.startedFs=!0,t()})).catch((()=>t())):t()}initLayout(){var t,e;const n=this.option("parentEl")||document.body,i=r(this.localize(this.option("tpl.main")||""));if(i){if(i.setAttribute("id",`fancybox-${this.id}`),i.setAttribute("aria-label",this.localize("{{MODAL}}")),i.classList.toggle(re,this.isCompact),P(i,this.option("mainClass")||""),P(i,le),this.container=i,this.footer=i.querySelector(".fancybox__footer"),n.appendChild(i),P(document.documentElement,Qt),be&&ye||(be=document.createElement("span"),P(be,"fancybox-focus-guard"),be.setAttribute(de,"0"),be.setAttribute(ie,"true"),be.setAttribute("aria-label","Focus guard"),ye=be.cloneNode(),null===(t=i.parentElement)||void 0===t||t.insertBefore(be,i),null===(e=i.parentElement)||void 0===e||e.append(ye)),i.addEventListener("mousedown",(t=>{xe=t.pageX,we=t.pageY,_(i,oe)})),this.option("closeExisting"))for(const t of Te.values())t.id!==this.id&&t.close();else this.option("animated")&&(P(i,se),setTimeout((()=>{this.isClosing()||_(i,se)}),350));this.emit("initLayout")}}initCarousel(t){const e=this.container;if(!e)return;const n=e.querySelector(".fancybox__carousel");if(!n)return;const i=this.carousel=new Q(n,g({},{slides:t,transition:"fade",Panzoom:{lockAxis:this.option("dragToClose")?"xy":"x",infinite:!!this.option("dragToClose")&&"y"},Dots:!1,Navigation:{classes:{container:"fancybox__nav",button:"f-button",isNext:"is-next",isPrev:"is-prev"}},initialPage:this.option("startIndex"),l10n:this.option("l10n")},this.option("Carousel")||{}));i.on("*",((t,e,...n)=>{this.emit(`Carousel.${e}`,t,...n)})),i.on(["ready","change"],(()=>{this.manageCaption()})),this.on("Carousel.removeSlide",((t,e,n)=>{this.clearContent(n),n.state=void 0})),i.on("Panzoom.touchStart",(()=>{var t,e;this.isCompact||this.endIdle(),(null===(t=document.activeElement)||void 0===t?void 0:t.closest(".f-thumbs"))&&(null===(e=this.container)||void 0===e||e.focus())})),i.on("settle",(()=>{this.idleTimer||this.isCompact||!this.option("idle")||this.setIdle(),this.option("autoFocus")&&!this.isClosing&&this.checkFocus()})),this.option("dragToClose")&&(i.on("Panzoom.afterTransform",((t,e)=>{const n=this.getSlide();if(n&&o(n.el))return;const i=this.container;if(i){const t=Math.abs(e.current.f),n=t<1?"":Math.max(.5,Math.min(1,1-t/e.contentRect.fitHeight*1.5));i.style.setProperty("--fancybox-ts",n?"0s":""),i.style.setProperty("--fancybox-opacity",n+"")}})),i.on("Panzoom.touchEnd",((t,e,n)=>{var i;const s=this.getSlide();if(s&&o(s.el))return;if(e.isMobile&&document.activeElement&&-1!==["TEXTAREA","INPUT"].indexOf(null===(i=document.activeElement)||void 0===i?void 0:i.nodeName))return;const r=Math.abs(e.dragOffset.y);"y"===e.lockedAxis&&(r>=200||r>=50&&e.dragOffset.time<300)&&(n&&n.cancelable&&n.preventDefault(),this.close(n,"f-throwOut"+(e.current.f<0?"Up":"Down")))}))),i.on("change",(t=>{var e;let n=null===(e=this.getSlide())||void 0===e?void 0:e.triggerEl;if(n){const e=new CustomEvent("slideTo",{bubbles:!0,cancelable:!0,detail:t.page});n.dispatchEvent(e)}})),i.on(["refresh","change"],(t=>{const e=this.container;if(!e)return;for(const n of e.querySelectorAll("[data-fancybox-current-index]"))n.innerHTML=t.page+1;for(const n of e.querySelectorAll("[data-fancybox-count]"))n.innerHTML=t.pages.length;if(!t.isInfinite){for(const n of e.querySelectorAll("[data-fancybox-next]"))t.page<t.pages.length-1?(n.removeAttribute(ue),n.removeAttribute(de)):(n.setAttribute(ue,""),n.setAttribute(de,"-1"));for(const n of e.querySelectorAll("[data-fancybox-prev]"))t.page>0?(n.removeAttribute(ue),n.removeAttribute(de)):(n.setAttribute(ue,""),n.setAttribute(de,"-1"))}const n=this.getSlide();if(!n)return;let i=n.downloadSrc||"";i||"image"!==n.type||n.error||!ge(n[pe])||(i=n[pe]);for(const t of e.querySelectorAll("[data-fancybox-download]")){const e=n.downloadFilename;i?(t.removeAttribute(ue),t.removeAttribute(de),t.setAttribute(fe,i),t.setAttribute(he,e||i),t.setAttribute("target","_blank")):(t.setAttribute(ue,""),t.setAttribute(de,"-1"),t.removeAttribute(fe),t.removeAttribute(he))}})),this.emit("initCarousel")}attachEvents(){const t=this,e=t.container;if(!e)return;e.addEventListener("click",t.onClick,{passive:!1,capture:!1}),e.addEventListener("wheel",t.onWheel,{passive:!1,capture:!1}),document.addEventListener("keydown",t.onKeydown,{passive:!1,capture:!0}),document.addEventListener("visibilitychange",t.onVisibilityChange,!1),document.addEventListener("mousemove",t.onMousemove),t.option("trapFocus")&&document.addEventListener("focus",t.onFocus,!0),window.addEventListener("resize",t.onResize);const n=window.visualViewport;n&&(n.addEventListener("scroll",t.onResize),n.addEventListener("resize",t.onResize))}detachEvents(){const t=this,e=t.container;if(!e)return;document.removeEventListener("keydown",t.onKeydown,{passive:!1,capture:!0}),e.removeEventListener("wheel",t.onWheel,{passive:!1,capture:!1}),e.removeEventListener("click",t.onClick,{passive:!1,capture:!1}),document.removeEventListener("mousemove",t.onMousemove),window.removeEventListener("resize",t.onResize);const n=window.visualViewport;n&&(n.removeEventListener("resize",t.onResize),n.removeEventListener("scroll",t.onResize)),document.removeEventListener("visibilitychange",t.onVisibilityChange,!1),document.removeEventListener("focus",t.onFocus,!0)}scale(){const t=this.container;if(!t)return;const e=window.visualViewport,n=Math.max(1,(null==e?void 0:e.scale)||1);let i="",o="",s="";if(e&&n>1){let t=`${e.offsetLeft}px`,r=`${e.offsetTop}px`;i=e.width*n+"px",o=e.height*n+"px",s=`translate3d(${t}, ${r}, 0) scale(${1/n})`}t.style.transform=s,t.style.width=i,t.style.height=o}onClick(t){var e;const{container:n,isCompact:i}=this;if(!n||this.isClosing())return;!i&&this.option("idle")&&this.resetIdle();const o=t.composedPath()[0];if(o.closest(".fancybox-spinner")||o.closest("[data-fancybox-close]"))return t.preventDefault(),void this.close(t);if(o.closest("[data-fancybox-prev]"))return t.preventDefault(),void this.prev();if(o.closest("[data-fancybox-next]"))return t.preventDefault(),void this.next();if("click"===t.type&&0===t.detail)return;if(Math.abs(t.pageX-xe)>30||Math.abs(t.pageY-we)>30)return;const s=document.activeElement;if(me()&&s&&n.contains(s))return;if(i&&"image"===(null===(e=this.getSlide())||void 0===e?void 0:e.type))return void(this.clickTimer?(clearTimeout(this.clickTimer),this.clickTimer=null):this.clickTimer=setTimeout((()=>{this.toggleIdle(),this.clickTimer=null}),350));if(this.emit("click",t),t.defaultPrevented)return;let r=!1;if(o.closest(".fancybox__content")){if(s){if(s.closest("[contenteditable]"))return;o.matches(it)||s.blur()}if(me())return;r=this.option("contentClick")}else o.closest(".fancybox__carousel")&&!o.matches(it)&&(r=this.option("backdropClick"));"close"===r?(t.preventDefault(),this.close(t)):"next"===r?(t.preventDefault(),this.next()):"prev"===r&&(t.preventDefault(),this.prev())}onWheel(t){const e=t.target;let n=this.option("wheel",t);e.closest(".fancybox__thumbs")&&(n="slide");const i="slide"===n,o=[-t.deltaX||0,-t.deltaY||0,-t.detail||0].reduce((function(t,e){return Math.abs(e)>Math.abs(t)?e:t})),r=Math.max(-1,Math.min(1,o)),a=Date.now();this.pwt&&a-this.pwt<300?i&&t.preventDefault():(this.pwt=a,this.emit("wheel",t,r),t.defaultPrevented||("close"===n?(t.preventDefault(),this.close(t)):"slide"===n&&(s(e)||(t.preventDefault(),this[r>0?"prev":"next"]()))))}onScroll(){window.scrollTo(Ee,Se)}onKeydown(t){if(!this.isTopmost())return;this.isCompact||!this.option("idle")||this.isClosing()||this.resetIdle();const e=t.key,n=this.option("keyboard");if(!n)return;const i=t.composedPath()[0],o=document.activeElement&&document.activeElement.classList,s=o&&o.contains("f-button")||i.dataset.carouselPage||i.dataset.carouselIndex;if("Escape"!==e&&!s&&C(i)&&(i.isContentEditable||-1!==["TEXTAREA","OPTION","INPUT","SELECT","VIDEO"].indexOf(i.nodeName)))return;if("Tab"===t.key?P(this.container,oe):_(this.container,oe),t.ctrlKey||t.altKey||t.shiftKey)return;this.emit("keydown",e,t);const r=n[e];r&&"function"==typeof this[r]&&(t.preventDefault(),this[r]())}onResize(){const t=this.container;if(!t)return;const e=this.isCompact;t.classList.toggle(re,e),this.manageCaption(this.getSlide()),this.isCompact?this.clearIdle():this.endIdle(),this.scale(),this.emit("resize")}onFocus(t){this.isTopmost()&&this.checkFocus(t)}onMousemove(t){this.prevMouseMoveEvent=t,!this.isCompact&&this.option("idle")&&this.resetIdle()}onVisibilityChange(){"visible"===document.visibilityState?this.checkFocus():this.endIdle()}manageCloseBtn(t){const e=this.optionFor(t,"closeButton")||!1;if("auto"===e){const t=this.plugins.Toolbar;if(t&&t.state===Ut.Ready)return}if(!e)return;if(!t.contentEl||t.closeBtnEl)return;const n=this.option("tpl.closeButton");if(n){const e=r(this.localize(n));t.closeBtnEl=t.contentEl.appendChild(e),t.el&&P(t.el,"has-close-btn")}}manageCaption(t=void 0){var e,n;const i="fancybox__caption",o=this.container;if(!o)return;_(o,ce);const s=this.isCompact||this.option("commonCaption"),r=!s;if(this.caption&&this.stop(this.caption),r&&this.caption&&(this.caption.remove(),this.caption=null),s&&!this.caption)for(const t of(null===(e=this.carousel)||void 0===e?void 0:e.slides)||[])t.captionEl&&(t.captionEl.remove(),t.captionEl=void 0,_(t.el,ce),null===(n=t.el)||void 0===n||n.removeAttribute("aria-labelledby"));if(t||(t=this.getSlide()),!t||s&&!this.isCurrentSlide(t))return;const a=t.el;let l=this.optionFor(t,"caption","");if(!l)return void(s&&this.caption&&this.animate(this.caption,"f-fadeOut",(()=>{this.caption&&(this.caption.innerHTML="")})));let c=null;if(r){if(c=t.captionEl||null,a&&!c){const e=i+`_${this.id}_${t.index}`;c=document.createElement("div"),P(c,i),c.setAttribute("id",e),t.captionEl=a.appendChild(c),P(a,ce),a.setAttribute("aria-labelledby",e)}}else c=this.caption,c||(c=o.querySelector("."+i)),c||(c=document.createElement("div"),c.dataset.fancyboxCaption="",P(c,i),(this.footer||o).prepend(c)),P(o,ce),this.caption=c;c&&(c.innerHTML="",ge(l)||"number"==typeof l?c.innerHTML=l+"":l instanceof HTMLElement&&c.appendChild(l))}checkFocus(t){this.focus(t)}focus(t){var e;if(this.ignoreFocusChange)return;const n=document.activeElement||null,i=(null==t?void 0:t.target)||null,o=this.container,s=null===(e=this.carousel)||void 0===e?void 0:e.viewport;if(!o||!s)return;if(!t&&n&&o.contains(n))return;const r=this.getSlide(),a=r&&r.state===lt.Ready?r.el:null;if(!a||a.contains(n)||o===n)return;t&&t.cancelable&&t.preventDefault(),this.ignoreFocusChange=!0;const l=Array.from(o.querySelectorAll(it));let c=[],u=null;for(let t of l){const e=!t.offsetParent||!!t.closest('[aria-hidden="true"]'),n=a&&a.contains(t),i=!s.contains(t);if(t===o||(n||i)&&!e){c.push(t);const e=t.dataset.origTabindex;void 0!==e&&e&&(t.tabIndex=parseFloat(e)),t.removeAttribute("data-orig-tabindex"),!t.hasAttribute("autoFocus")&&u||(u=t)}else{const e=void 0===t.dataset.origTabindex?t.getAttribute("tabindex")||"":t.dataset.origTabindex;e&&(t.dataset.origTabindex=e),t.tabIndex=-1}}let d=null;t?(!i||c.indexOf(i)<0)&&(d=u||o,c.length&&(n===ye?d=c[0]:this.lastFocus!==o&&n!==be||(d=c[c.length-1]))):d=r&&"image"===r.type?o:u||o,d&&ot(d),this.lastFocus=document.activeElement,this.ignoreFocusChange=!1}next(){const t=this.carousel;t&&t.pages.length>1&&t.slideNext()}prev(){const t=this.carousel;t&&t.pages.length>1&&t.slidePrev()}jumpTo(...t){this.carousel&&this.carousel.slideTo(...t)}isTopmost(){var t;return(null===(t=_e.getInstance())||void 0===t?void 0:t.id)==this.id}animate(t=null,e="",n){if(!t||!e)return void(n&&n());this.stop(t);const i=o=>{o.target===t&&t.dataset.animationName&&(t.removeEventListener("animationend",i),delete t.dataset.animationName,n&&n(),_(t,e))};t.dataset.animationName=e,t.addEventListener("animationend",i),P(t,e)}stop(t){t&&t.dispatchEvent(new CustomEvent("animationend",{bubbles:!1,cancelable:!0,currentTarget:t}))}setContent(t,e="",n=!0){if(this.isClosing())return;const i=t.el;if(!i)return;let o=null;if(C(e)?o=e:(o=r(e+""),C(o)||(o=document.createElement("div"),o.innerHTML=e+"")),["img","picture","iframe","video","audio"].includes(o.nodeName.toLowerCase())){const t=document.createElement("div");t.appendChild(o),o=t}C(o)&&t.filter&&!t.error&&(o=o.querySelector(t.filter)),o&&C(o)?(P(o,"fancybox__content"),t.id&&o.setAttribute("id",t.id),"none"!==o.style.display&&"none"!==getComputedStyle(o).getPropertyValue("display")||(o.style.display=t.display||this.option("defaultDisplay")||"flex"),i.classList.add(`has-${t.error?"error":t.type||"unknown"}`),i.prepend(o),t.contentEl=o,n&&this.revealContent(t),this.manageCloseBtn(t),this.manageCaption(t)):this.setError(t,"{{ELEMENT_NOT_FOUND}}")}revealContent(t,e){const n=t.el,i=t.contentEl;n&&i&&(this.emit("reveal",t),this.hideLoading(t),t.state=lt.Opening,(e=this.isOpeningSlide(t)?void 0===e?this.optionFor(t,"showClass"):e:"f-fadeIn")?this.animate(i,e,(()=>{this.done(t)})):this.done(t))}done(t){this.isClosing()||(t.state=lt.Ready,this.emit("done",t),P(t.el,"is-done"),this.isCurrentSlide(t)&&this.option("autoFocus")&&queueMicrotask((()=>{var e;null===(e=t.panzoom)||void 0===e||e.updateControls(),this.option("autoFocus")&&this.focus()})),this.isOpeningSlide(t)&&(_(this.container,le),!this.isCompact&&this.option("idle")&&this.setIdle()))}isCurrentSlide(t){const e=this.getSlide();return!(!t||!e)&&e.index===t.index}isOpeningSlide(t){var e,n;return null===(null===(e=this.carousel)||void 0===e?void 0:e.prevPage)&&t&&t.index===(null===(n=this.getSlide())||void 0===n?void 0:n.index)}showLoading(t){t.state=lt.Loading;const e=t.el;e&&(P(e,ae),this.emit("loading",t),t.spinnerEl||setTimeout((()=>{if(!this.isClosing()&&!t.spinnerEl&&t.state===lt.Loading){let n=r(T);P(n,"fancybox-spinner"),t.spinnerEl=n,e.prepend(n),this.animate(n,"f-fadeIn")}}),250))}hideLoading(t){const e=t.el;if(!e)return;const n=t.spinnerEl;this.isClosing()?null==n||n.remove():(_(e,ae),n&&this.animate(n,"f-fadeOut",(()=>{n.remove()})),t.state===lt.Loading&&(this.emit("loaded",t),t.state=lt.Ready))}setError(t,e){if(this.isClosing())return;const n=new Event("error",{bubbles:!0,cancelable:!0});if(this.emit("error",n,t),n.defaultPrevented)return;t.error=e,this.hideLoading(t),this.clearContent(t);const i=document.createElement("div");i.classList.add("fancybox-error"),i.innerHTML=this.localize(e||"<p>{{ERROR}}</p>"),this.setContent(t,i)}clearContent(t){if(void 0===t.state)return;this.emit("clearContent",t),t.contentEl&&(t.contentEl.remove(),t.contentEl=void 0);const e=t.el;e&&(_(e,"has-error"),_(e,"has-unknown"),_(e,`has-${t.type||"unknown"}`)),t.closeBtnEl&&t.closeBtnEl.remove(),t.closeBtnEl=void 0,t.captionEl&&t.captionEl.remove(),t.captionEl=void 0,t.spinnerEl&&t.spinnerEl.remove(),t.spinnerEl=void 0}getSlide(){var t;const e=this.carousel;return(null===(t=null==e?void 0:e.pages[null==e?void 0:e.page])||void 0===t?void 0:t.slides[0])||void 0}close(t,e){if(this.isClosing())return;const n=new Event("shouldClose",{bubbles:!0,cancelable:!0});if(this.emit("shouldClose",n,t),n.defaultPrevented)return;t&&t.cancelable&&(t.preventDefault(),t.stopPropagation());const i=()=>{this.proceedClose(t,e)};this.startedFs&&ve&&ve.isFullscreen()?Promise.resolve(ve.exit()).then((()=>i())):i()}clearIdle(){this.idleTimer&&clearTimeout(this.idleTimer),this.idleTimer=null}setIdle(t=!1){const e=()=>{this.clearIdle(),this.idle=!0,P(this.container,"is-idle"),this.emit("setIdle")};if(this.clearIdle(),!this.isClosing())if(t)e();else{const t=this.option("idle");t&&(this.idleTimer=setTimeout(e,t))}}endIdle(){this.clearIdle(),this.idle&&!this.isClosing()&&(this.idle=!1,_(this.container,"is-idle"),this.emit("endIdle"))}resetIdle(){this.endIdle(),this.setIdle()}toggleIdle(){this.idle?this.endIdle():this.setIdle(!0)}toggleFullscreen(){ve&&(ve.isFullscreen()?ve.exit():ve.request().then((()=>{this.startedFs=!0})))}isClosing(){return[at.Closing,at.CustomClosing,at.Destroy].includes(this.state)}proceedClose(t,e){var n,i;this.state=at.Closing,this.clearIdle(),this.detachEvents();const o=this.container,s=this.carousel,r=this.getSlide(),a=r&&this.option("placeFocusBack")?r.triggerEl||this.option("triggerEl"):null;if(a&&(tt(a)?ot(a):a.focus()),o&&(_(o,le),P(o,"is-closing"),o.setAttribute(ie,"true"),this.option("animated")&&P(o,se),o.style.pointerEvents="none"),s){s.clearTransitions(),null===(n=s.panzoom)||void 0===n||n.destroy(),null===(i=s.plugins.Navigation)||void 0===i||i.detach();for(const t of s.slides){t.state=lt.Closing,this.hideLoading(t);const e=t.contentEl;e&&this.stop(e);const n=null==t?void 0:t.panzoom;n&&(n.stop(),n.detachEvents(),n.detachObserver()),this.isCurrentSlide(t)||s.emit("removeSlide",t)}}Ee=window.scrollX,Se=window.scrollY,window.addEventListener("scroll",this.onScroll),this.emit("close",t),this.state!==at.CustomClosing?(void 0===e&&r&&(e=this.optionFor(r,"hideClass")),e&&r?(this.animate(r.contentEl,e,(()=>{s&&s.emit("removeSlide",r)})),setTimeout((()=>{this.destroy()}),500)):this.destroy()):setTimeout((()=>{this.destroy()}),500)}destroy(){var t;if(this.state===at.Destroy)return;window.removeEventListener("scroll",this.onScroll),this.state=at.Destroy,null===(t=this.carousel)||void 0===t||t.destroy();const e=this.container;e&&e.remove(),Te.delete(this.id);const n=_e.getInstance();n?n.focus():(be&&(be.remove(),be=null),ye&&(ye.remove(),ye=null),_(document.documentElement,Qt),(()=>{if(!et)return;const t=document,e=t.body;e.classList.remove(te),e.style.setProperty(ne,""),t.documentElement.style.setProperty(ee,"")})(),this.emit("destroy"))}static bind(t,e,n){if(!et)return;let i,o="",s={};if(void 0===t?i=document.body:ge(t)?(i=document.body,o=t,"object"==typeof e&&(s=e||{})):(i=t,ge(e)&&(o=e),"object"==typeof n&&(s=n||{})),!i||!C(i))return;o=o||"[data-fancybox]";const r=_e.openers.get(i)||new Map;r.set(o,s),_e.openers.set(i,r),1===r.size&&i.addEventListener("click",_e.fromEvent)}static unbind(t,e){let n,i="";if(ge(t)?(n=document.body,i=t):(n=t,ge(e)&&(i=e)),!n)return;const o=_e.openers.get(n);o&&i&&o.delete(i),i&&o||(_e.openers.delete(n),n.removeEventListener("click",_e.fromEvent))}static destroy(){let t;for(;t=_e.getInstance();)t.destroy();for(const t of _e.openers.keys())t.removeEventListener("click",_e.fromEvent);_e.openers=new Map}static fromEvent(t){if(t.defaultPrevented)return;if(t.button&&0!==t.button)return;if(t.ctrlKey||t.metaKey||t.shiftKey)return;let e=t.composedPath()[0];const n=e.closest("[data-fancybox-trigger]");if(n){const t=n.dataset.fancyboxTrigger||"",i=document.querySelectorAll(`[data-fancybox="${t}"]`),o=parseInt(n.dataset.fancyboxIndex||"",10)||0;e=i[o]||e}if(!(e&&e instanceof Element))return;let i,o,s,r;if([..._e.openers].reverse().find((([t,n])=>!(!t.contains(e)||![...n].reverse().find((([n,a])=>{let l=e.closest(n);return!!l&&(i=t,o=n,s=l,r=a,!0)}))))),!i||!o||!s)return;r=r||{},t.preventDefault(),e=s;let a=[],l=g({},rt,r);l.event=t,l.triggerEl=e,l.delegate=n;const c=l.groupAll,u=l.groupAttr,d=u&&e?e.getAttribute(`${u}`):"";if((!e||d||c)&&(a=[].slice.call(i.querySelectorAll(o))),e&&!c&&(a=d?a.filter((t=>t.getAttribute(`${u}`)===d)):[e]),!a.length)return;const h=_e.getInstance();return h&&h.options.triggerEl&&a.indexOf(h.options.triggerEl)>-1?void 0:(e&&(l.startIndex=a.indexOf(e)),_e.fromNodes(a,l))}static fromSelector(t,e,n){let i=null,o="",s={};if(ge(t)?(i=document.body,o=t,"object"==typeof e&&(s=e||{})):t instanceof HTMLElement&&ge(e)&&(i=t,o=e,"object"==typeof n&&(s=n||{})),!i||!o)return!1;const r=_e.openers.get(i);return!!r&&(s=g({},r.get(o)||{},s),!!s&&_e.fromNodes(Array.from(i.querySelectorAll(o)),s))}static fromNodes(t,e){e=g({},rt,e||{});const n=[];for(const i of t){const t=i.dataset||{},o=t[pe]||i.getAttribute(fe)||i.getAttribute("currentSrc")||i.getAttribute(pe)||void 0;let s;const r=e.delegate;let a;r&&n.length===e.startIndex&&(s=r instanceof HTMLImageElement?r:r.querySelector("img:not([aria-hidden])")),s||(s=i instanceof HTMLImageElement?i:i.querySelector("img:not([aria-hidden])")),s&&(a=s.currentSrc||s[pe]||void 0,!a&&s.dataset&&(a=s.dataset.lazySrc||s.dataset[pe]||void 0));const l={src:o,triggerEl:i,thumbEl:s,thumbElSrc:a,thumbSrc:a};for(const e in t){let n=t[e]+"";n="false"!==n&&("true"===n||n),l[e]=n}n.push(l)}return new _e(n,e)}static getInstance(t){return t?Te.get(t):Array.from(Te.values()).reverse().find((t=>!t.isClosing()&&t))||null}static getSlide(){var t;return(null===(t=_e.getInstance())||void 0===t?void 0:t.getSlide())||null}static show(t=[],e={}){return new _e(t,e)}static next(){const t=_e.getInstance();t&&t.next()}static prev(){const t=_e.getInstance();t&&t.prev()}static close(t=!0,...e){if(t)for(const t of Te.values())t.close(...e);else{const t=_e.getInstance();t&&t.close(...e)}}}Object.defineProperty(_e,"version",{enumerable:!0,configurable:!0,writable:!0,value:"5.0.35"}),Object.defineProperty(_e,"defaults",{enumerable:!0,configurable:!0,writable:!0,value:rt}),Object.defineProperty(_e,"Plugins",{enumerable:!0,configurable:!0,writable:!0,value:Jt}),Object.defineProperty(_e,"openers",{enumerable:!0,configurable:!0,writable:!0,value:new Map});var Pe=n(379),ke=n.n(Pe),Oe=n(795),Me=n.n(Oe),Ae=n(569),Le=n.n(Ae),ze=n(565),De=n.n(ze),je=n(216),Ie=n.n(je),Re=n(589),Fe=n.n(Re),Ne=n(583),He={};He.styleTagTransform=Fe(),He.setAttributes=De(),He.insert=Le().bind(null,"head"),He.domAPI=Me(),He.insertStyleElement=Ie(),ke()(Ne.Z,He),Ne.Z&&Ne.Z.locals&&Ne.Z.locals,window.jQuery=e(),window.$=e(),_e.bind("[data-fancybox]",{}),document.addEventListener("DOMContentLoaded",(function(){var t=document.querySelector(".main-menu"),e=t.querySelector(".main-menu-btn-desktop"),n=document.querySelector("body");e.addEventListener("click",(()=>{t.classList.toggle("active"),t.classList.contains("active")?e.setAttribute("data-cursor-bubble-text","Свернуть меню"):e.setAttribute("data-cursor-bubble-text","Раскрыть меню")})),document.addEventListener("click",(function(n){document.querySelector(".main-menu").contains(n.target)||e.contains(n.target)||t.classList.remove("active")})),document.querySelector(".main-menu-btn").addEventListener("click",(()=>{n.classList.toggle("main-menu-open")}))})),e()(document).ready((function(){e()("ul.main-menu__catalog-uslug").on("click","li:not(.catalog-uslug__item--link)",(function(){e()(this).hasClass("active")?(e()(this).removeClass("active"),e()(this).children(".catalog-menu__list").slideUp()):(e()("ul.main-menu__catalog-uslug li.active").removeClass("active"),e()("ul.main-menu__catalog-uslug li .catalog-menu__list").slideUp(),e()(this).addClass("active"),e()(this).children(".catalog-menu__list").slideDown())}))}))}()}();