@charset "UTF-8";
/* Base */ /* Reset and base styles  */
* {
  padding: 0px;
  margin: 0px;
  border: none;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

/* Links */
a, a:link, a:visited {
  text-decoration: none;
}

a:hover {
  text-decoration: none;
}

/* Common */
aside, nav, footer, header, section, main {
  display: block;
}

h1, h2, h3, h4, h5, h6, p {
  font-size: inherit;
  font-weight: inherit;
}

ul, ul li {
  list-style: none;
}

img {
  vertical-align: top;
}

img, svg {
  max-width: 100%;
  height: auto;
}

address {
  font-style: normal;
}

/* Form */
input, textarea, button, select {
  font-family: inherit;
  font-size: inherit;
  color: inherit;
  background-color: transparent;
}

input::-ms-clear {
  display: none;
}

button, input[type=submit] {
  display: inline-block;
  box-shadow: none;
  background-color: transparent;
  background: none;
  cursor: pointer;
}

input:focus, input:active,
button:focus, button:active {
  outline: none;
}

button::-moz-focus-inner {
  padding: 0;
  border: 0;
}

label {
  cursor: pointer;
}

legend {
  display: block;
}

:root {
  --container-width: 1200px;
  --container-padding: 15px;
  --font-main: "Onest", sans-serif;
  --font-accent: "Manrope", sans-serif;
  --font-titles: var(--font-main);
  --font-legacy: "mainfont";
  --font-weight-thin: 100;
  --font-weight-extralight: 200;
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;
  --font-thin: var(--font-main);
  --font-extralight: var(--font-main);
  --font-light: var(--font-main);
  --font-regular: var(--font-main);
  --font-medium: var(--font-main);
  --font-semibold: var(--font-main);
  --font-bold: var(--font-main);
  --font-extrabold: var(--font-main);
  --font-black: var(--font-main);
  --page-bg: #fff;
  --text-color: #000;
  --accent: #ac182c;
  --link-color: #2578c8;
  --laptop-size: 1199px;
  --tablet-size: 959px;
  --mobile-size: 599px;
  --accent-color: #FF752B;
  --alternate-accent-color: #15A0E5;
  --grey-text: #707070;
  --white-text: #FFF;
  --black-text: #282828;
  --bgr-white: #FFFFFF;
  --bgr-gray: gray;
  --menu-color: #F5F5F5;
  --bgr-color__dark-gray-original: #1E1E1E;
  --bgr-color__dark-gray: #121213;
  --bgr-color__bgr-white: #F6F7FA;
  --bgr-color__bgr-grey: #E6E8EB;
  --borders__on-dark: #454545;
  --borders__on-white: #E2E3E6;
  --borders__on-grey: #D6D7D9;
  --headers__on-dark: #D9D9D9;
  --text__on-dark: #BBBBBB;
  --white: #FFFFFF;
  --padding-w1700: 3.5rem;
  --padding-w993: 3rem;
  --padding-w992: 2rem;
  --padding-w767: 2rem;
  --padding-w576: 1.6rem;
  --animation-primary: all .5s cubic-bezier(.7, 0, .3, 1);
  --animation-fast: all .3s cubic-bezier(.7, 0, .3, 1);
  --animation-smooth: all .7s cubic-bezier(.7, 0, .3, 1);
  --animation-slow: all .9s cubic-bezier(.7, 0, .3, 1);
  --borders-color: rgba(178,188,195,.5);
}

.dark {
  --page-bg: #252526;
  --text-color: #fff;
}

@font-face {
  font-family: FirasansBook;
  font-display: swap;
  src: url("../fonts/FirasansBook.woff2") format("woff2"), url("../fonts/FirasansBook.woff") format("woff");
  font-weight: 400;
  font-style: normal;
}
@font-face {
  font-family: Montserrat;
  font-display: swap;
  src: url("../fonts/Montserrat-Bold.woff2") format("woff2"), url("../fonts/Montserrat-Bold.woff") format("woff");
  font-weight: 700;
  font-style: normal;
}
@font-face {
  font-family: Montserrat;
  font-display: swap;
  src: url("../fonts/Montserrat-BoldItalic.woff2") format("woff2"), url("../fonts/Montserrat-BoldItalic.woff") format("woff");
  font-weight: 400;
  font-style: normal;
}
@font-face {
  font-family: Montserrat;
  font-display: swap;
  src: url("../fonts/Montserrat-Regular.woff2") format("woff2"), url("../fonts/Montserrat-Regular.woff") format("woff");
  font-weight: 400;
  font-style: normal;
}
html {
  font-size: 62.5%;
  height: 100%;
  width: 100%;
  scroll-behavior: smooth;
}
@media (max-width: 1500px) {
  html {
    font-size: 62.5%;
  }
}
@media (max-width: 992px) {
  html {
    font-size: 60%;
  }
}
@media (max-width: 767px) {
  html {
    font-size: 58%;
  }
}
@media (max-width: 576px) {
  html {
    font-size: 56%;
  }
}

body {
  background-color: var(--bgr-white);
  color: var(--black-text);
  font-family: var(--font-main);
  font-size: 1.6rem;
}
@media (max-width: 767px) {
  body {
    font-size: 1.4rem;
  }
}
@media (max-width: 576px) {
  body {
    font-size: 1.2rem;
  }
}

.btn {
  display: inline-flex;
}

.btn-click {
  cursor: pointer;
  border: 0;
  color: var(--black-text);
  background: transparent;
  border-radius: 10rem !important;
  min-width: 1rem;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  text-decoration: none;
  will-change: transform;
  outline: 0;
  transform: translateZ(0) rotate(0.001deg);
  padding: 1.7rem 3rem !important;
  margin: 0 !important;
  text-decoration: none !important;
  font-size: 1.4rem;
}
@media (max-width: 992px) {
  .btn-click {
    font-size: 1.2rem;
    height: 4.8rem;
    padding: 1.7rem 2rem !important;
  }
}

.btn-click-small-padding {
  padding: 1rem 1.5rem !important;
}

.btn-click-white {
  cursor: pointer;
  border: 0;
  color: var(--headers__on-dark);
  background: transparent;
  border-radius: 10rem !important;
  min-width: 1rem;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  text-decoration: none;
  will-change: transform;
  outline: 0;
  transform: translateZ(0) rotate(0.001deg);
  padding: 1.7rem 3rem !important;
  margin: 0 !important;
  text-decoration: none !important;
  font-size: 1.4rem;
}
@media (max-width: 992px) {
  .btn-click-white {
    font-size: 1.2rem;
  }
}

.btn-bgr-dark {
  background: var(--bgr-color__dark-gray-original);
}

.btn-bgr-white {
  background: var(--headers__on-dark);
}

.btn-fill {
  background: var(--borders__on-dark);
  position: absolute;
  width: 150%;
  height: 200%;
  border-radius: 50%;
  top: -50%;
  left: -25%;
  transform: translate3d(0, -76%, 0);
  will-change: transform;
  transition: background-color ease-in-out 0.25s;
}

.btn-text {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  z-index: 2;
  color: var(--text__on-dark);
  position: relative;
  transform: rotate(0.001deg);
  pointer-events: none;
  will-change: transform, color;
}

.btn-normal .btn-text .btn-text-inner {
  color: var(--headers__on-dark) !important;
  display: flex;
  align-items: center;
  gap: 1rem;
  justify-content: center;
}

.pointernone {
  pointer-events: none;
}

.zindex {
  z-index: 9999999999999;
  pointer-events: all;
}

.btn-text-inner img {
  width: 2.4rem;
}

@media (max-width: 992) {
  .btn-text-inner img {
    width: 1.4rem;
  }
}
@media (min-width: 993) {
  .btn-text-inner img {
    width: 1.5rem;
  }
}
@media (max-width: 1400) {
  .btn-text-inner img {
    width: 1.6rem;
  }
}
.uppercase {
  text-transform: uppercase;
}

.main-button {
  font-size: 1.4rem;
  color: var(--headers__on-dark);
  background: var(--bgr-color__dark-gray-original);
  padding: 0.3rem 0.3rem 0.3rem 2.5rem;
  border: 1px solid var(--headers__on-dark);
  border-radius: 50rem;
  align-items: center;
  height: 5.8rem;
  display: flex;
  justify-content: flex-start;
  position: relative;
  z-index: 1;
}
@media (max-width: 992px) {
  .main-button {
    font-size: 1.2rem;
    padding: 0.3rem 0.3rem 0.3rem 0.9rem;
    height: 4.6rem;
  }
}
.main-button__icon {
  border-radius: 10rem;
  background: linear-gradient(180deg, #FFF 0%, #BABABA 100%);
  box-shadow: -1px 0px 10.3px 0px rgba(0, 0, 0, 0.11);
  width: 5.2rem;
  height: 5.2rem;
  display: flex;
  justify-content: end;
  align-items: center;
  margin-left: 1.5rem;
  position: absolute;
  right: 0.2rem;
  transition: var(--animation-smooth);
  z-index: -1;
}
.main-button__icon img {
  height: 50%;
  padding-right: 1.3rem;
}
@media (max-width: 992px) {
  .main-button__icon img {
    padding-right: 1rem;
  }
}
@media (max-width: 992px) {
  .main-button__icon {
    width: 4rem;
    height: 4rem;
    margin-left: 1rem;
  }
}
.main-button:hover {
  color: var(--borders__on-dark);
}
.main-button:hover .main-button__icon {
  width: calc(100% - 0.4rem);
}
.main-button__dummy {
  visibility: hidden;
  width: 5.2rem;
  height: 5.2rem;
  margin-left: 1.5rem;
}
@media (max-width: 992px) {
  .main-button__dummy {
    width: 4rem;
    height: 4rem;
    margin-left: 1rem;
  }
}

::-moz-selection {
  background-color: var(--borders__on-dark);
  color: var(--headers__on-dark);
}

::selection {
  background-color: var(--borders__on-dark);
  color: var(--headers__on-dark);
}

.docs {
  display: grid;
  line-height: 1.5;
}
.docs p {
  margin: 1rem 0;
}
.docs ul,
.docs ol {
  padding-left: 2rem;
}
.docs ul li,
.docs ol li {
  list-style: disc;
  margin-bottom: 0.5rem;
}
.docs ol li {
  list-style: decimal;
}
.docs section, .docs section.docs {
  padding: 40px 0;
}
.docs section + section {
  border-top: 1px solid #dae5e9;
}
.docs small {
  font-size: 1rem;
  color: rgb(172, 172, 172);
}
.docs .title-1:first-child,
.docs .title-2:first-child {
  margin-top: 0 !important;
}

.test {
  width: 600px;
  height: 300px;
  margin: 50px auto;
  background-color: #999;
  background-position: center center; /* x y */
  background-size: cover;
  background-repeat: no-repeat;
  background-image: url("./../img/project-02.jpg");
}
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .test {
    background-image: url("./../img/<EMAIL>");
  }
}

.test-2 {
  width: 600px;
  height: 300px;
  margin: 50px auto;
  background-color: #999;
  background-position: center center; /* x y */
  background-size: cover;
  background-repeat: no-repeat;
  background-image: image-set(url("./../img/project-02.jpg") 1x, url("./../img/<EMAIL>") 2x);
}

.font-1 {
  font-family: "Montserrat";
  font-weight: 700;
  font-style: italic;
}

.font-2 {
  font-family: "FirasansBook";
  font-weight: 400;
}

/* Отключить при необходимости */
.none {
  display: none !important;
}

.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  border: 0;
  padding: 0;
  white-space: nowrap;
  clip-path: inset(100%);
  clip: rect(0 0 0 0);
  overflow: hidden;
}

.no-scroll {
  overflow-y: hidden;
}

@media only screen and (max-width: 576px) {
  .ssm-hidden {
    display: none !important;
  }
}
@media only screen and (max-width: 767px) {
  .sm-hidden {
    display: none !important;
  }
  .sm-center {
    text-align: center;
    margin: 0 auto;
  }
  .mt-2-only-sm {
    margin-top: 2rem;
  }
}
@media only screen and (max-width: 992px) {
  .md-hidden {
    display: none !important;
  }
}
@media only screen and (max-width: 1200px) {
  .lg-hidden {
    display: none !important;
  }
}
@media only screen and (min-width: 768px) {
  .sm-visible {
    display: none !important;
  }
}
@media only screen and (min-width: 993px) {
  .md-visible {
    display: none !important;
  }
}
@media only screen and (min-width: 1201px) {
  .lg-visible {
    display: none !important;
  }
}
@media screen and (min-device-width: 768px) and (max-device-width: 992px) {
  .md-only-hidden {
    display: none !important;
  }
}
.container {
  margin: 0 auto;
}
@media (min-width: 1700px) {
  .container {
    margin: auto 8rem;
    padding: 0 5rem;
  }
}
@media (min-width: 993px) and (max-width: 1699px) {
  .container {
    margin: auto 5rem;
    padding: 0 3rem;
  }
}
@media (max-width: 992px) {
  .container {
    margin: auto 3rem;
    padding: 0 2rem;
  }
}
@media (max-width: 767px) {
  .container {
    margin: auto 2rem;
    padding: 0 2rem;
  }
}
@media (max-width: 576px) {
  .container {
    margin: auto 1.6rem;
    padding: 0 1.6rem;
  }
}

.container-grid-section {
  margin: 0 auto;
  height: 100%;
  min-height: inherit;
  position: relative;
  z-index: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
@media (min-width: 1700px) {
  .container-grid-section {
    margin: auto 8rem;
  }
}
@media (min-width: 993px) and (max-width: 1699px) {
  .container-grid-section {
    margin: auto 5rem;
  }
}
@media (max-width: 992px) {
  .container-grid-section {
    margin: auto 3rem;
  }
}
@media (max-width: 767px) {
  .container-grid-section {
    margin: auto 2rem;
  }
}
@media (max-width: 576px) {
  .container-grid-section {
    margin: auto 1.6rem;
  }
}

.container-no-border {
  margin: 0 auto;
}
@media (min-width: 1700px) {
  .container-no-border {
    margin: auto 8rem;
    padding: 0 5rem;
  }
}
@media (min-width: 993px) and (max-width: 1699px) {
  .container-no-border {
    margin: auto 5rem;
    padding: 0 3rem;
  }
}
@media (max-width: 992px) {
  .container-no-border {
    margin: auto 3rem;
    padding: 0 2rem;
  }
}
@media (max-width: 767px) {
  .container-no-border {
    margin: auto 2rem;
    padding: 0 2rem;
  }
}
@media (max-width: 576px) {
  .container-no-border {
    margin: auto 1.6rem;
    padding: 0 1.6rem;
  }
}

.container-inside-container {
  position: relative;
}
@media (min-width: 1700px) {
  .container-inside-container {
    padding: 0 5rem;
  }
}
@media (min-width: 993px) and (max-width: 1699px) {
  .container-inside-container {
    padding: 0 3rem;
  }
}
@media (max-width: 992px) {
  .container-inside-container {
    padding: 0 2rem;
  }
}
@media (max-width: 767px) {
  .container-inside-container {
    padding: 0 2rem;
  }
}
@media (max-width: 576px) {
  .container-inside-container {
    padding: 0 1.6rem;
  }
}

.lines {
  pointer-events: none;
  position: absolute;
  z-index: -5;
  background: transparent;
  width: 100%;
  height: 100%;
  opacity: 1;
  display: flex;
  flex-grow: 0;
  flex-shrink: 0;
  flex-wrap: nowrap;
}
.lines__empty {
  width: calc(25% - 1px);
  background: transparent;
  height: 100%;
  opacity: 0.1;
}
@media (max-width: 992px) {
  .lines__empty {
    width: calc(33.33333% - 1px);
  }
}
.lines__line {
  width: 1px;
  background-color: var(--borders__on-dark);
  height: 100%;
}
.lines__line--invisible {
  width: 1px;
  background-color: transparent;
  height: 100%;
}

.lines-md-2 {
  pointer-events: none;
  position: absolute;
  z-index: -5;
  background: transparent;
  width: 100%;
  height: 100%;
  opacity: 1;
  display: flex;
  flex-grow: 0;
  flex-shrink: 0;
  flex-wrap: nowrap;
}
.lines-md-2__empty {
  width: calc(25% - 1px);
  background: transparent;
  height: 100%;
  opacity: 0.1;
}
@media (max-width: 992px) {
  .lines-md-2__empty {
    width: calc(50% - 1px);
  }
}
.lines-md-2__line {
  width: 1px;
  background-color: var(--borders__on-dark);
  height: 100%;
}

.main-menu-open .container {
  border-left: 1px solid rgba(255, 0, 0, 0);
  border-right: 1px solid rgba(255, 0, 0, 0);
}

.disable-padding {
  padding: 0 !important;
}

.borders-l-r {
  border-left: 1px solid var(--borders__on-dark);
  border-right: 1px solid var(--borders__on-dark);
}

.borders-t {
  border-top: 1px solid var(--borders__on-dark);
}

.borders-b {
  border-bottom: 1px solid var(--borders__on-dark);
}

.borders-t-b {
  border-top: 1px solid var(--borders__on-dark);
  border-bottom: 1px solid var(--borders__on-dark);
}

.borders-all {
  border: 1px solid var(--borders__on-dark);
}

.disable-borders {
  border: none !important;
}

.flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.flex_wrap {
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.flex_aic {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.flex_aife {
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: flex-end;
}

.flex_jcc {
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.flex_jcsb {
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.flex_row {
  flex-direction: row;
}

@media (max-width: 767px) {
  .flex_mobile-column {
    flex-direction: column;
  }
}
.container-right {
  /* overflow-x: hidden; */
  padding-left: calc((100% - var(--container-width)) / 2 + var(--container-padding));
}
@media (max-width: var(--laptop-size)) {
  .container-right {
    padding-left: var(--container-padding);
  }
}

.grid-section__content {
  font-size: 2.5rem;
  line-height: 140%;
}

.container-left {
  /* overflow-x: hidden; */
  padding-right: calc((100% - var(--container-width)) / 2 + var(--container-padding));
}
@media (max-width: var(--laptop-size)) {
  .container-left {
    padding-right: var(--container-padding);
  }
}

.mt-auto {
  margin-top: auto;
}

.mt-50 {
  margin-top: 5rem;
}

.pt-50 {
  padding-top: 5rem;
}
@media (max-width: 992px) {
  .pt-50 {
    padding-bottom: 4rem;
  }
}
@media (max-width: 767px) {
  .pt-50 {
    padding-bottom: 2rem;
  }
}
@media (max-width: 567px) {
  .pt-50 {
    padding-bottom: 1.6rem;
  }
}

.pb-50 {
  padding-bottom: 5rem;
}
@media (max-width: 992px) {
  .pb-50 {
    padding-bottom: 4rem;
  }
}
@media (max-width: 767px) {
  .pb-50 {
    padding-bottom: 2rem;
  }
}
@media (max-width: 567px) {
  .pb-50 {
    padding-bottom: 1.6rem;
  }
}

.pb-30 {
  padding-bottom: 3rem;
}
@media (max-width: 992px) {
  .pb-30 {
    padding-bottom: 3rem;
  }
}
@media (max-width: 767px) {
  .pb-30 {
    padding-bottom: 2rem;
  }
}
@media (max-width: 567px) {
  .pb-30 {
    padding-bottom: 1.6rem;
  }
}

.gap-3 {
  gap: 3rem;
}

.mb-3 {
  margin-bottom: 3rem;
}
@media (max-width: 992px) {
  .mb-3 {
    margin-bottom: 2rem;
  }
}
@media (max-width: 567px) {
  .mb-3 {
    margin-bottom: 1.6rem;
  }
}

.spares-block ul {
  margin-bottom: 1rem;
}

.spares-block ul li {
  list-style: disc;
  list-style-position: inside;
  padding-bottom: 1rem;
  line-height: 150%;
}

.mw770 {
  max-width: 77rem;
}
@media (max-width: 992px) {
  .mw770 {
    max-width: 100%;
  }
}
.m-0-auto {
  margin: 0 auto;
}

html, body {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.footer {
  margin-top: auto;
}

.footer {
  padding: 60px 0;
  background-color: #e3e3e3;
}

/* Blocks */
.footer {
  background-color: rgb(39, 39, 39);
  padding: 50px 0;
  font-size: 32px;
  color: #fff;
}
.footer h1 {
  font-size: 32px;
}
.footer a {
  color: #fff;
  text-decoration: underline;
}
@media (max-width: 1200px) {
  .footer {
    font-size: 26px;
  }
}

.footer__copyright {
  padding: 10px 0;
  font-size: 16px;
}

.header {
  position: relative;
}

.main-header__section {
  position: relative;
}

@media (max-width: 1220px) {
  .header__nav {
    display: none;
  }
}

.sub-header__section {
  border-bottom: 0.1rem solid var(--borders-color);
}

.sub-header {
  text-transform: uppercase;
  font-weight: var(--font-weight-semibold);
  font-size: 1.3rem;
  color: var(--text-color);
  padding: 1rem 0;
}
.sub-header a {
  color: var(--text-color);
  transition: color 0.3s ease;
  cursor: pointer;
}
.sub-header a:hover {
  color: var(--accent-color);
}
.sub-header a:hover .sub-header__icon-svg {
  color: var(--accent-color);
}

.sub-header__socials {
  gap: 2rem;
}
@media (max-width: 900px) {
  .sub-header__socials {
    gap: 0.5rem 2rem;
  }
}
@media (min-width: 1200px) {
  .sub-header__socials {
    gap: 4rem;
  }
}

.sub-header__calculate-phone {
  gap: 2rem;
  justify-content: end;
}
@media (max-width: 900px) {
  .sub-header__calculate-phone {
    gap: 0.5rem 2rem;
  }
}
@media (min-width: 1200px) {
  .sub-header__calculate-phone {
    gap: 4rem;
  }
}

.sub-header__item {
  display: inline-flex;
  align-items: center;
  gap: 0.8rem;
  color: var(--text-color);
  transition: color 0.3s ease;
}
.sub-header__item:hover {
  color: var(--accent-color);
}
.sub-header__item:hover .sub-header__item-svg {
  color: var(--accent-color);
}

.sub-header__item-svg {
  width: 2rem;
  height: 2rem;
  color: var(--text-color);
  transition: color 0.3s ease;
  flex-shrink: 0;
}
@media (max-width: 460px) {
  .sub-header__item-svg {
    display: none;
  }
}

@media (max-width: 420px) {
  .sub-header__item-email,
  .sub-header__item-calculate {
    display: none;
  }
}
.phone {
  font-weight: var(--font-weight-bold);
}

.main-header__section {
  border-bottom: 0.1rem solid var(--borders-color);
}

.main-header {
  align-items: center;
  font-size: 1.3rem;
  color: var(--text-color);
  padding: 1rem 0;
}

.logo-descriptor {
  gap: 3rem;
  margin-right: 3rem;
}
@media (max-width: 992px) {
  .logo-descriptor {
    margin-right: 0;
    justify-content: space-between;
    width: 100%;
  }
}
@media (max-width: 576px) {
  .logo-descriptor {
    gap: 5.5rem;
  }
}

.logo-and-descriptor {
  gap: 3rem;
}

.logo {
  width: 17.2rem;
  height: 8.6rem;
}

.descriptor {
  text-transform: none;
  font-weight: var(--font-weight-regular);
  max-width: 11rem;
  font-size: 1.3rem;
  color: var(--grey-text);
  padding-top: 1.1rem;
}
@media (max-width: 767px) {
  .descriptor {
    display: none;
  }
}

@media (min-width: 993px) {
  .main-menu {
    flex-grow: 1;
  }
  .main-menu-btn-desktop {
    display: flex;
    flex-wrap: nowrap;
    color: var(--white-text);
    padding: 1.3rem 1.2rem;
    font-size: 1.2rem;
    font-weight: 600;
    text-transform: uppercase;
    border-radius: 0.7rem;
    background: #15a0e5 !important;
    height: 5rem;
    align-items: center;
    width: 16rem;
    white-space: nowrap;
  }
}
@media (min-width: 993px) and (max-width: 992px) {
  .main-menu-btn-desktop {
    display: none;
  }
}
@media (min-width: 993px) {
  .main-menu-btn-desktop:hover {
    background: linear-gradient(180deg, #3cb5f1 0%, #15a0e5 100%) !important;
    transition: background 0.3s ease;
  }
  button.main-menu-btn-desktop {
    background: #15a0e5 !important;
    background-color: #15a0e5 !important;
  }
  button.main-menu-btn-desktop:hover {
    background: linear-gradient(180deg, #3cb5f1 0%, #15a0e5 100%) !important;
  }
  .main-menu-btn-desktop svg {
    margin-right: 1rem;
    width: 2.4rem;
    height: 2.4rem;
    flex-shrink: 0;
  }
  .main-menu-btn-desktop svg path {
    transition-property: all;
    transition-delay: 0.1s;
    transition-duration: 0.6s;
    transition-timing-function: ease-out;
    stroke: white;
  }
  .main-menu-btn-desktop:hover svg path {
    transform: scaleX(1);
    transition-property: all;
    transition-delay: 0.1s;
    transition-duration: 0.6s;
    transition-timing-function: ease-out;
  }
  .main-menu-btn-desktop:hover svg path:nth-child(1) {
    transform: scaleX(1.2);
  }
  .main-menu-btn-desktop:hover svg path:nth-child(2) {
    transform: scaleX(1);
  }
  .main-menu-btn-desktop:hover svg path:nth-child(3) {
    transform: scaleX(1.3) translateX(0.01rem);
  }
  .nav__btn-menu {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    gap: 3rem;
    align-items: center;
  }
  .main-menu__list {
    display: flex;
    justify-content: space-between;
    width: 100%;
  }
  .main-menu__list li:last-child a {
    padding: 0.5rem 0 0.5rem 0.7rem !important;
  }
}
@media (min-width: 993px) and (max-width: 1279px) {
  .main-menu__list {
    justify-content: flex-start;
    flex-wrap: wrap;
  }
}
@media (min-width: 993px) {
  .main-menu__list li a {
    color: var(--black-text);
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 2rem; /* 166.667% */
    text-transform: uppercase;
    display: inline-flex;
    padding: 0.5rem 0.7rem;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    background: linear-gradient(180deg, rgba(224, 224, 224, 0) 0%, rgba(165, 163, 163, 0) 100%);
    white-space: nowrap;
  }
  .main-menu__list li a:hover {
    color: var(--accent-color);
  }
  .main-menu__list--icon a {
    display: flex;
    gap: 1rem;
  }
  .main-menu__list--icon a svg {
    fill: var(--headers__on-dark);
  }
  .main-menu__list--icon a:hover svg {
    fill: var(--bgr-color__dark-gray);
  }
  .main-menu-modal {
    background: var(--bgr-color__dark-gray-original);
    left: 0;
    right: 0;
    margin-top: 0.1rem;
    opacity: 0;
    position: absolute;
    top: 100%;
    visibility: collapse;
    width: auto;
    z-index: 10;
    border-bottom: 1px solid var(--borders__on-dark);
    font-size: 3.3rem;
  }
  .active .main-menu-modal {
    opacity: 1;
    visibility: visible;
    z-index: 10;
    -webkit-animation: menushow 0.5s cubic-bezier(0.7, 0, 0.3, 1);
    animation: menushow 0.5s cubic-bezier(0.7, 0, 0.3, 1);
  }
  @keyframes menushow {
    0% {
      -webkit-transform: translateY(-10%) scale(100%);
      transform: translateY(-10%) scale(100%);
      opacity: 0;
    }
    100% {
      -webkit-transform: translateY(0%);
      transform: translateY(0%);
      opacity: 100%;
    }
  }
  .main-menu__catalog-uslug {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 0;
  }
  .catalog-uslug__item {
    padding: 3rem 3rem 3rem;
    display: flex;
    flex-direction: column;
    gap: 3rem;
    position: relative;
    overflow: hidden;
    overflow-y: scroll;
    height: 40vh;
  }
  .catalog-uslug__item:hover {
    animation: bgrcolor;
    animation-delay: 0.8s;
    animation-duration: 0ms;
    animation-fill-mode: forwards;
  }
  @keyframes bgrcolor {
    0% {
      background-color: unset;
    }
    100% {
      background-color: var(--bgr-color__dark-gray);
    }
  }
  .catalog-uslug__h3 {
    z-index: 3;
    color: #FFF;
    mix-blend-mode: difference;
    font-size: 2rem;
    font-weight: 700;
    line-height: 140%;
  }
  .catalog-uslug__item img {
    z-index: 3;
    border-radius: 10rem;
    width: 100%;
    object-fit: cover;
  }
  .catalog-menu__list {
    z-index: 3;
    font-size: 1.4rem;
    line-height: 140%;
    list-style: disc;
    align-self: flex-start;
    display: none;
  }
  .catalog-uslug__item:hover .catalog-menu__list {
    display: block;
    animation: catmenulist-in 0.6s cubic-bezier(0.7, 0, 0.3, 1);
    animation-fill-mode: forwards;
  }
  @keyframes catmenulist-in {
    0% {
      -webkit-transform: translateY(200%);
      transform: translateY(200%);
      opacity: 0%;
      display: none;
    }
    100% {
      -webkit-transform: translateY(0%) scale(100%);
      transform: translateY(0%) scale(100%);
      opacity: 100%;
      display: block;
    }
  }
  .catalog-menu__list li {
    list-style: circle;
    list-style-position: outside;
    margin-left: 1.5rem;
    list-style-type: disc;
    color: var(--white);
    padding-bottom: 1.2rem;
  }
  .catalog-menu__list li:last-child {
    padding-bottom: 0;
  }
  .catalog-menu__list a {
    color: var(--text__on-dark);
  }
  .catalog-menu__list a:hover {
    text-decoration: underline;
  }
  .catalog-uslug__item img {
    animation: catmenuimg-in 0.8s cubic-bezier(0.7, 0, 0.3, 1);
    animation-fill-mode: forwards;
  }
  .catalog-uslug__item:hover img {
    animation: catmenuimg-out 0.8s cubic-bezier(0.7, 0, 0.3, 1);
    animation-fill-mode: forwards;
  }
  @keyframes catmenuimg-out {
    0% {
      -webkit-transform: translateY(0%) scale(100%);
      transform: translateY(0%) scale(100%);
      opacity: 100%;
      display: block;
    }
    100% {
      -webkit-transform: translateY(200%);
      transform: translateY(200%);
      opacity: 0%;
      display: none;
    }
  }
  @keyframes catmenuimg-in {
    0% {
      -webkit-transform: translateY(200%);
      transform: translateY(200%);
      opacity: 0%;
      display: none;
    }
    100% {
      -webkit-transform: translateY(0%) scale(100%);
      transform: translateY(0%) scale(100%);
      opacity: 100%;
      display: block;
    }
  }
  .catalog-uslug__item::before {
    content: " ";
    width: 100%;
    height: 0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--bgr-color__dark-gray);
    border-radius: inherit;
    transition: 0.8s;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    margin: auto;
    z-index: 2;
  }
  .catalog-uslug__item:hover:before {
    width: 100%;
    height: 100%;
  }
  .catalog-uslug__item:nth-child(1),
  .catalog-uslug__item:nth-child(2),
  .catalog-uslug__item:nth-child(3) {
    border-right: 1px solid var(--borders__on-dark);
    border-bottom: 1px solid var(--borders__on-dark);
  }
  .catalog-uslug__item:nth-child(4) {
    border-bottom: 1px solid var(--borders__on-dark);
  }
  .catalog-uslug__item:nth-child(5),
  .catalog-uslug__item:nth-child(6),
  .catalog-uslug__item:nth-child(7) {
    border-right: 1px solid var(--borders__on-dark);
  }
}
@media (max-width: 992px) {
  .main-menu {
    flex-direction: column;
    -webkit-box-orient: vertical;
    -webkit-box-direction: reverse;
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    background: var(--menu-color);
    -webkit-box-shadow: 0 8px 9px 0 rgba(0, 0, 0, 0.07);
    box-shadow: 0 8px 9px 0 rgba(0, 0, 0, 0.07);
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    gap: 2rem;
    height: 100vh;
    justify-content: flex-start;
    left: 0;
    padding-top: 2rem;
    position: absolute;
    top: 0;
    -webkit-transform: translateX(-120%);
    -ms-transform: translateX(-120%);
    transform: translateX(-120%);
    -webkit-transition: 0.2s;
    transition: 0.2s;
    width: 100%;
    z-index: 97;
    overflow-y: scroll;
  }
  .main-menu__list {
    font-size: 1.4rem;
    margin-bottom: 3rem;
    margin: auto 0;
    padding: 0 1.6rem;
  }
  .main-menu__list a {
    color: var(--black-text);
    display: flex;
    gap: 0.5rem;
    font-size: 1.7rem;
    padding: 1.5rem 0;
    border-bottom: 1px solid var(--borders-color);
  }
  .main-menu__list a:hover, .main-menu__list a:focus {
    padding-left: 1.5rem;
    color: var(--accent-color);
  }
  .main-menu__list a:active {
    color: var(--accent-color);
  }
  .main-menu__list a svg {
    fill: var(--black-text);
    width: 1.6rem;
    height: 1.6rem;
  }
  .main-menu__list li {
    transition: linear 0.4s;
  }
  .main-menu__list li:last-child {
    padding-bottom: 0;
  }
  .main-menu__catalog-uslug {
    padding-bottom: 2rem;
  }
  .main-menu__list::before,
  .main-menu__catalog-uslug::before {
    content: attr(data-name) "";
    font-size: 1.6rem;
    padding: 2rem 0;
    color: var(--accent-color);
    display: block;
    text-transform: uppercase;
    font-weight: var(--font-weight-semibold);
  }
  .main-menu__list--icon a {
    display: flex;
    gap: 0.5rem;
  }
  .main-menu__list--icon a svg {
    fill: var(--headers__on-dark);
    width: 1.6rem;
    height: 1.6rem;
  }
  .sm-disable-margin {
    margin: auto 0;
  }
  .catalog-uslug__item {
    position: relative;
    -webkit-transition: 0.4s;
    -o-transition: 0.4s;
    transition: 0.4s;
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: auto;
    grid-column-gap: 0px;
    grid-row-gap: 1rem;
    align-items: center;
    padding: 0.7rem 0;
    line-height: 3rem;
  }
  .catalog-uslug__item h3 {
    padding-left: 1rem;
    font-size: 1.4rem;
    line-height: 140%;
  }
  .catalog-uslug__item ul {
    grid-area: 2/1/3/3;
  }
  .catalog-uslug__item img {
    grid-area: 1/1/2/2;
  }
  .catalog-uslug__item.active {
    background: var(--bgr-color__dark-gray-original);
  }
  .catalog-uslug__item.active .accordeon-list-btn {
    font-weight: 700;
  }
  .catalog-uslug__item.active .accordeon-list-btn:after {
    -webkit-box-shadow: inset 0 0 0 0.1rem #c3beab;
    box-shadow: inset 0 0 0 0.1rem #c3beab;
    -webkit-transform: translateY(-50%) rotate(-180deg);
    -ms-transform: translateY(-50%) rotate(-180deg);
    transform: translateY(-50%) rotate(-180deg);
  }
  .catalog-uslug__item:not(:last-child) {
    border-bottom: 0.1rem solid var(--borders__on-dark);
  }
  .catalog-uslug__item ul {
    display: none;
    font-size: 1rem;
    list-style: none;
    margin: 0 0 1.5rem 1.5rem;
    padding: 0;
  }
  .catalog-uslug__item ul li {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    padding: 0.75rem 0.8rem;
    margin-right: 1.5rem;
    font-size: 1.4rem;
  }
  .catalog-uslug__item ul li a {
    color: var(--text__on-dark);
    line-height: 140%;
  }
  .catalog-uslug__item ul li:not(:last-child) {
    border-bottom: 0.1rem solid var(--borders__on-dark);
  }
  .accordeon-list-btn,
  .catalog-uslug__item ul a {
    text-decoration: none;
  }
  .catalog-uslug__item img {
    width: 100%;
    border-radius: 5rem;
    height: 100%;
    object-fit: cover;
  }
  .catalog-uslug__item ul li {
    padding: 1.5rem 0;
  }
  .catalog-uslug__h3 {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    border: none;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    font-size: 1.2rem;
    padding: 0.7rem 2.4rem;
    pointer-events: none;
    position: relative;
    width: 100%;
  }
  .catalog-uslug__h3:focus,
  .catalog-uslug__h3:hover {
    color: #2b2e35;
  }
  .catalog-uslug__h3:after {
    background: url(./../img/arrow-down.svg) no-repeat 50%;
    background-size: contain;
    border-radius: 0.5rem;
    content: "";
    display: block;
    height: 1.6rem;
    position: absolute;
    right: 0.8rem;
    top: 50%;
    -webkit-transform: translateY(-50%) rotate(0deg);
    -ms-transform: translateY(-50%) rotate(0deg);
    transform: translateY(-50%) rotate(0deg);
    -webkit-transition: 0.4s;
    -o-transition: 0.4s;
    transition: 0.4s;
    width: 1.6rem;
  }
  .catalog-uslug__item.active .catalog-uslug__h3:after {
    -webkit-transform: translateY(-50%) rotate(-180deg);
    -ms-transform: translateY(-50%) rotate(-180deg);
    transform: translateY(-50%) rotate(-180deg);
  }
  .catalog-uslug__item--link .catalog-uslug__link.catalog-uslug__h3 {
    padding-left: 1rem;
    font-size: 1.4rem;
    line-height: 140%;
  }
  .catalog-uslug__item--link .catalog-uslug__link.catalog-uslug__h3:after {
    display: none;
  }
  .catalog-uslug__item--link:hover .catalog-uslug__link.catalog-uslug__h3 {
    color: var(--accent-color);
  }
  .top-header {
    margin: 0 -2rem;
  }
  .top-header__logo {
    max-width: 50%;
    margin-right: auto;
    padding-left: 2rem;
  }
  .header {
    width: 100%;
  }
  .mobil-menu__info {
    margin-top: 2rem;
    margin-bottom: 4rem;
  }
  .top-header__contacts--grafic {
    padding-top: 0.6rem;
    text-align: center;
    padding-bottom: 2rem;
  }
  .top-header__messengers {
    margin-right: 3rem;
  }
  .top-header__messengers__icons {
    gap: 3rem;
    justify-content: center;
  }
}
@media (max-width: 576px) {
  .top-header {
    margin: 0 -1.6rem;
  }
}
.main-menu-btn {
  background: var(--menu-color);
  border-radius: 0.7rem;
  border: 0.125rem solid var(--borders-color);
  height: 7.4rem;
  overflow: hidden;
  padding: 0;
  position: relative;
  -webkit-transition: 0.6s;
  transition: 0.6s;
  width: 7.4rem;
  z-index: 99;
}

.main-menu-btn:hover {
  background: var(--accent-color);
  border: none;
}

.main-menu-btn .open {
  height: 100%;
  left: 0;
  opacity: 1;
  padding: 2rem;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 1;
}

.main-menu-open .main-menu-btn .open {
  opacity: 0;
  z-index: -1;
}

.main-menu-btn .open span {
  background: var(--black-text);
  display: block;
  height: 0.25rem;
  position: relative;
  -webkit-transition: 0.6s;
  transition: 0.6s;
  width: 100%;
  border-radius: 0.7rem;
}

.main-menu-btn .open span + span {
  margin-top: 0.75rem;
  width: 2.8rem;
}

.main-menu-btn .open span + span + span {
  margin-top: 0.75rem;
  width: 2.2rem;
}

.main-menu-btn .open:after {
  bottom: 1rem;
  color: var(--black-text);
  content: attr(data-attr);
  display: block;
  font-size: 1rem;
  font-weight: 700;
  left: 50%;
  line-height: 1.2;
  opacity: 1;
  position: absolute;
  text-transform: uppercase;
  -webkit-transform: translate(-50%);
  -ms-transform: translate(-50%);
  transform: translate(-50%);
  -webkit-transition: 0.6s;
  transition: 0.6s;
  width: 5rem;
}

.main-menu-btn .close {
  color: var(--black-text);
  height: 100%;
  left: 0;
  opacity: 0;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: -1;
}

.main-menu-btn .close svg {
  height: 2.7rem;
  left: 50%;
  -o-object-fit: cover;
  object-fit: cover;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(-50%, -50%) rotate(0);
  -ms-transform: translate(-50%, -50%) rotate(0);
  transform: translate(-50%, -50%) rotate(0);
  -webkit-transition: 0.6s;
  transition: 0.6s;
  width: 2.7rem;
}

.main-menu-btn .close:after {
  bottom: -2rem;
  color: var(--bgr-color__dark-gray);
  content: attr(data-attr);
  display: block;
  font-size: 0.8rem;
  font-weight: 700;
  left: 50%;
  line-height: 1.2;
  opacity: 0;
  position: absolute;
  text-transform: uppercase;
  -webkit-transform: translate(-50%);
  -ms-transform: translate(-50%);
  transform: translate(-50%);
  -webkit-transition: 0.6s;
  transition: 0.6s;
  width: 5rem;
}

.main-menu-btn:hover .open span {
  background: #404246;
  -webkit-transform: translateY(-0.1rem);
  -ms-transform: translateY(-0.1rem);
  transform: translateY(-0.1rem);
}

.main-menu-btn:hover .open span + span {
  width: 100%;
}

.main-menu-btn:hover .open:after {
  bottom: 1.4rem;
  opacity: 1;
}

.main-menu-open .main-menu-btn .close {
  opacity: 1;
  z-index: 1;
}

.main-menu-open .main-menu-btn:hover .close {
  color: var(--bgr-color__dark-gray);
}

.main-menu-open .main-menu-btn:hover .close svg {
  -webkit-transform: translate(-50%, calc(-50% - 1rem)) rotate(-180deg);
  -ms-transform: translate(-50%, calc(-50% - 1rem)) rotate(-180deg);
  transform: translate(-50%, calc(-50% - 1rem)) rotate(-180deg);
}

.main-menu-open .main-menu-btn:hover .close:after {
  bottom: 1.4rem;
  opacity: 1;
  visibility: visible;
}

.main-menu-open .main-menu {
  -webkit-transform: translateX(0);
  -ms-transform: translateX(0);
  transform: translateX(0);
}

@media only screen and (min-width: 993px) {
  .main-menu-btn {
    display: none;
  }
}
@media only screen and (max-width: 992px) {
  .body-hidden {
    overflow: hidden;
  }
}
@media only screen and (max-width: 1400px) {
  .hide-on-1400 {
    display: none;
  }
}
.icons-wrapper {
  padding: 30px 0;
  display: flex;
  column-gap: 30px;
}

.icon {
  fill: transparent;
  stroke: transparent;
  width: 62px;
  height: 62px;
}

.icon--heart-line {
  fill: rgb(241, 68, 131);
}

.icon--id-card-line {
  fill: rgb(51, 51, 51);
}

.icon--search-line {
  fill: rgb(28, 176, 80);
}

.icon--user-star {
  fill: rgb(26, 134, 235);
}

.icon--user {
  stroke: rgb(26, 134, 235);
  transition: all 0.2s ease-in;
}
.icon--user:hover {
  stroke: rgb(17, 193, 90);
}

/* Nav Icon */
.mobile-nav-btn {
  --time: 0.1s;
  --width: 40px;
  --height: 30px;
  --line-height: 4px;
  --spacing: 6px;
  --color: #000;
  --radius: 4px;
  /* Fixed height and width */
  /* height: var(--height); */
  /* width: var(--width); */
  /* Dynamic height and width */
  height: calc(var(--line-height) * 3 + var(--spacing) * 2);
  width: var(--width);
  display: flex;
  justify-content: center;
  align-items: center;
}

.nav-icon {
  position: relative;
  width: var(--width);
  height: var(--line-height);
  background-color: var(--color);
  border-radius: var(--radius);
}

.nav-icon::before,
.nav-icon::after {
  content: "";
  display: block;
  position: absolute;
  left: 0;
  width: var(--width);
  height: var(--line-height);
  border-radius: var(--radius);
  background-color: var(--color);
  transition: transform var(--time) ease-in, top var(--time) linear var(--time);
}

.nav-icon::before {
  /* top: calc(var(--line-height) * -2); */
  top: calc(-1 * (var(--line-height) + var(--spacing)));
}

.nav-icon::after {
  /* top: calc(var(--line-height) * 2); */
  top: calc(var(--line-height) + var(--spacing));
}

.nav-icon.nav-icon--active {
  background-color: transparent;
}

.nav-icon.nav-icon--active::before,
.nav-icon.nav-icon--active::after {
  top: 0;
  transition: top var(--time) linear, transform var(--time) ease-in var(--time);
}

.nav-icon.nav-icon--active::before {
  transform: rotate(45deg);
}

.nav-icon.nav-icon--active::after {
  transform: rotate(-45deg);
}

/* Layout */
.mobile-nav-btn {
  z-index: 999;
}

.nav {
  font-size: 18px;
}

.nav__list {
  display: flex;
  column-gap: 30px;
}

.title-1 {
  margin: 1em 0 0.5em;
  font-size: 38px;
  font-weight: 700;
  font-family: var(--font-titles);
}

.title-2 {
  margin: 1em 0 0.5em;
  font-size: 32px;
  font-weight: 700;
  font-family: var(--font-titles);
}

.title-3 {
  margin: 1em 0 0.5em;
  font-size: 26px;
  font-weight: 700;
  font-family: var(--font-titles);
}

.title-4 {
  margin: 1em 0 0.5em;
  font-size: 18px;
  font-weight: 700;
  font-family: var(--font-titles);
}

/* No styles code below. Only in modules */
/* Не пишите CSS код ниже. Только в подключаемых файлах */
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
